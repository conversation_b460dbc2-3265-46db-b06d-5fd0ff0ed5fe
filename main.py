import discord
from discord.ext import commands, tasks
from discord import app_commands, ui, ButtonStyle, Interaction, Embed, Color
import asyncio
import logging
import io
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from bson import ObjectId

# Import our custom modules
from database import DatabaseManager
from utils import (
    require_license, require_configuration, require_key_owner,
    is_server_owner, format_license_embed, validate_discord_id,
    parse_license_keys_from_text, get_guild_member_safe,
    get_channel_safe, get_role_safe
)

# ========== CONFIGURATION ==========
BOT_TOKEN = "MTM4OTQ2NjM4NjM5NjQ4MzcxNA.GuhczQ.gSeKISdznOw5jo2I8iXcNkkp7T5NSLp1SLalgg"
MONGO_URL = "mongodb+srv://rustymag:rrM46&<EMAIL>/leakin?retryWrites=true&w=majority&appName=Cluster0"
ADMIN_USER_ID = 1378705093301375026  # User who can add license keys
# ===================================

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot setup with necessary intents
intents = discord.Intents.default()
intents.presences = True
intents.members = True
intents.guilds = True
intents.message_content = True
# DM messages should be included in default intents

# Store users who currently have the role to track changes per server
server_users_with_role = {}

# Create bot instance
class MyBot(commands.Bot):
    def __init__(self):
        super().__init__(command_prefix='!', intents=intents)
        self.db = DatabaseManager(MONGO_URL)

# Initialize the bot
bot = MyBot()

# Make db available at module level for backward compatibility
db = bot.db

@bot.event
async def on_ready():
    """Called when the bot is ready"""
    logger.info(f'{bot.user} has connected to Discord!')
    
    # Set bot status
    await bot.change_presence(
        activity=discord.Activity(
            type=discord.ActivityType.watching,
            name="statuses… | join /leakin"
        )
    )
    
    # Connect to database
    try:
        bot.db.connect()
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        return

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")
    
    # Initialize role tracking for all servers
    for guild in bot.guilds:
        if bot.db.is_server_licensed(guild.id):
            config = bot.db.get_server_config(guild.id)
            if config and config.get('role_id'):
                role = guild.get_role(config['role_id'])
                if role:
                    server_users_with_role[guild.id] = set(member.id for member in role.members)
    
    # Start the status checking task
    if not check_users_status.is_running():
        check_users_status.start()

    # Cleanup temp voice channels on startup
    await cleanup_temp_voice_channels()

@tasks.loop(seconds=10)
async def check_users_status():
    """Check all users every 10 seconds for the trigger word in their status across all licensed servers"""
    
    # Batch role assignments and removals to avoid rate limits
    role_assignments = []
    role_removals = []
    
    for guild in bot.guilds:
        # Skip if server doesn't have a license
        if not db.is_server_licensed(guild.id):
            logger.debug(f"Skipping {guild.name} - no license")
            continue
            
        config = db.get_server_config(guild.id)
        if not config:
            logger.debug(f"Skipping {guild.name} - no configuration")
            continue
            
        role_id = config.get('role_id')
        channel_id = config.get('channel_id')
        log_channel_id = config.get('log_channel_id')
        ignored_users = config.get('ignored_users', [])
        trigger_word = config.get('trigger_word')
        
        if not all([role_id, channel_id, trigger_word]):
            logger.debug(f"Skipping {guild.name} - incomplete configuration (role_id: {role_id}, channel_id: {channel_id}, trigger_word: {trigger_word})")
            continue
            
        role = guild.get_role(role_id)
        channel = guild.get_channel(channel_id)
        log_channel = guild.get_channel(log_channel_id) if log_channel_id else None
        
        if not role or not channel:
            continue
            
        # Initialize server tracking if not exists
        if guild.id not in server_users_with_role:
            server_users_with_role[guild.id] = set()
            
        current_users_with_role = server_users_with_role[guild.id]
        
        # Check all members in the guild
        for member in guild.members:
            # Skip bots and ignored users
            if member.bot or member.id in ignored_users:
                continue
            
            # Check if user is offline and has the role - remove it
            if member.status == discord.Status.offline and member.id in current_users_with_role:
                await handle_role_removal(member, role, log_channel, guild.id)
                continue
            
            # Extract status text from custom status
            status_text = ""
            for activity in member.activities:
                if activity.type == discord.ActivityType.custom:
                    if hasattr(activity, 'name') and activity.name:
                        status_text = activity.name
                        break
                    elif hasattr(activity, 'state') and activity.state:
                        status_text = activity.state
                        break
            
            # Check if status contains the trigger word (case insensitive, substring match)
            if status_text:
                status_lower = status_text.lower()
                trigger_lower = trigger_word.lower()
                has_trigger = trigger_lower in status_lower
                user_has_role = member.id in current_users_with_role
                
                # Queue role assignment if trigger found and user doesn't have role
                if has_trigger and not user_has_role:
                    role_assignments.append((member, role, channel, guild.id, trigger_word, status_text))
                    logger.info(f"Queued role assignment for {member.display_name} - trigger '{trigger_word}' found")
                # Queue role removal if trigger not found and user has role
                elif not has_trigger and user_has_role:
                    role_removals.append((member, role, log_channel, guild.id))
            
            # If no status text but user has role, queue for removal
            elif member.id in current_users_with_role:
                role_removals.append((member, role, log_channel, guild.id))
    
    # Process batched role assignments with rate limiting
    if role_assignments:
        logger.info(f"Processing {len(role_assignments)} role assignments...")
        for i, (member, role, channel, guild_id, trigger_word, status_text) in enumerate(role_assignments):
            try:
                await handle_role_assignment(member, role, channel, guild_id, trigger_word)
                logger.info(f"Assigned role to {member.display_name} - trigger '{trigger_word}' in status: '{status_text}'")
                # Small delay to avoid rate limits (50ms between assignments)
                if i < len(role_assignments) - 1:  # Don't delay after the last one
                    await asyncio.sleep(0.05)
            except Exception as e:
                logger.error(f"Failed to assign role to {member.display_name}: {e}")
    
    # Process batched role removals with rate limiting
    if role_removals:
        logger.info(f"Processing {len(role_removals)} role removals...")
        for i, (member, role, log_channel, guild_id) in enumerate(role_removals):
            try:
                await handle_role_removal(member, role, log_channel, guild_id)
                logger.info(f"Removed role from {member.display_name}")
                # Small delay to avoid rate limits (50ms between removals)
                if i < len(role_removals) - 1:  # Don't delay after the last one
                    await asyncio.sleep(0.05)
            except Exception as e:
                logger.error(f"Failed to remove role from {member.display_name}: {e}")

async def handle_role_assignment(member, role, channel, guild_id, trigger_word):
    """Handle giving the role to a user with license validation"""
    try:
        # Check if server still has a valid license
        if not db.is_server_licensed(guild_id):
            logger.info(f"Skipping role assignment for {member.display_name} - server {guild_id} license is no longer valid")
            # Clean up tracking
            if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
                server_users_with_role[guild_id].remove(member.id)
            return
            
        # Check if user already has the role
        if role in member.roles:
            logger.debug(f"User {member.display_name} already has role {role.name}")
            return
            
        # Add the role
        await member.add_roles(role, reason=f"Trigger word '{trigger_word}' detected in status")
        
        # Send notification
        embed = discord.Embed(
            title="✅ Role Assigned",
            description=f"You've been given the {role.mention} role because your status contained the trigger word `{trigger_word}`.",
            color=discord.Color.green()
        )
        await channel.send(f"{member.mention}", embed=embed)
        
        # Update tracking
        if guild_id in server_users_with_role:
            server_users_with_role[guild_id].add(member.id)
        else:
            server_users_with_role[guild_id] = {member.id}
            
        logger.info(f"Assigned role {role.name} to {member.display_name} in guild {member.guild.name}")
        
    except discord.Forbidden:
        logger.error(f"Missing permissions to assign role {role.name} in {member.guild.name}")
        # Try to notify an admin
        try:
            await channel.send("❌ I don't have permission to assign roles. Please check my role permissions.", delete_after=30)
        except:
            pass
    except Exception as e:
        logger.error(f"Error assigning role to {member.display_name}: {e}")
        # Clean up tracking on error
        if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
            server_users_with_role[guild_id].remove(member.id)

async def handle_role_removal(member, role, log_channel, guild_id):
    """Handle removing the role from a user with license validation"""
    try:
        # Skip if server no longer has a valid license
        if not db.is_server_licensed(guild_id):
            logger.info(f"Skipping role removal for {member.display_name} - server {guild_id} license is no longer valid")
            # Clean up tracking
            if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
                server_users_with_role[guild_id].remove(member.id)
            return
            
        # Check if user has the role
        if role not in member.roles:
            # Clean up tracking if user doesn't have role but is in our tracking
            if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
                server_users_with_role[guild_id].remove(member.id)
            return
            
        # Remove the role
        await member.remove_roles(role, reason="Trigger word no longer in status, user went offline, or license was removed")
        
        # Update tracking
        if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
            server_users_with_role[guild_id].remove(member.id)
            
        logger.info(f"Removed role {role.name} from {member.display_name} in guild {member.guild.name}")
        
    except discord.Forbidden:
        logger.error(f"Missing permissions to remove role {role.name} in {member.guild.name}")
        # Clean up tracking on error
        if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
            server_users_with_role[guild_id].remove(member.id)
    except Exception as e:
        logger.error(f"Error removing role from {member.display_name}: {e}")
        # Clean up tracking on error
        if guild_id in server_users_with_role and member.id in server_users_with_role[guild_id]:
            server_users_with_role[guild_id].remove(member.id)

# ========== LICENSE MANAGEMENT COMMANDS ==========

@bot.tree.command(name="redeem-key", description="Redeem a license key for this server")
async def redeem_key(interaction: discord.Interaction, license_key: str):
    """Redeem a license key for the current server"""
    await interaction.response.defer(ephemeral=True)
    
    if not interaction.guild:
        await interaction.followup.send("❌ This command can only be used in a server!", ephemeral=True)
        return
    
    user_id = interaction.user.id
    server_id = interaction.guild.id
    
    # Check if user is the server owner
    if not is_server_owner(interaction.user, interaction.guild):
        embed = format_license_embed(
            "❌ Permission Denied",
            "Only the server owner can redeem license keys.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return
    
    # Check if server already has a license
    existing_license = db.get_server_license_key(server_id)
    if existing_license:
        embed = format_license_embed(
            "⚠️ Server Already Licensed",
            f"This server already has an active license key.\nKey: `{existing_license['key'][:8]}...`",
            discord.Color.orange()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return
    
    # Attempt to redeem the key
    success = db.redeem_license_key(license_key, user_id, server_id)
    
    if success:
        embed = format_license_embed(
            "✅ License Key Redeemed!",
            f"Successfully redeemed license key for **{interaction.guild.name}**\n\n"
            "**Next Steps:**\n"
            "Go to https://bot.leakin.cc to configure your server.",
            discord.Color.green()
        )
        logger.info(f"License key redeemed by {interaction.user} ({user_id}) for server {interaction.guild.name} ({server_id})")
    else:
        embed = format_license_embed(
            "❌ Invalid License Key",
            "The provided license key is invalid or has already been redeemed.",
            discord.Color.red()
        )
    
    await interaction.followup.send(embed=embed, ephemeral=True)

@bot.tree.command(name="transfer-key", description="Transfer your license key to a different server")
@require_key_owner(db)
async def transfer_key(interaction: discord.Interaction, license_key: str, server_id: str):
    """Transfer a license key to a different server"""
    await interaction.response.defer(ephemeral=True)
    
    # Validate server ID
    target_server_id = validate_discord_id(server_id)
    if not target_server_id:
        await interaction.followup.send("❌ Invalid server ID format.", ephemeral=True)
        return
    
    # Check if user owns the target server
    target_guild = bot.get_guild(target_server_id)
    if not target_guild:
        await interaction.followup.send("❌ Target server not found or bot is not in that server.", ephemeral=True)
        return
    
    if not is_server_owner(interaction.user, target_guild):
        embed = format_license_embed(
            "❌ Permission Denied",
            "You can only transfer license keys to servers you own.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return
    
    # Check if target server already has a license
    existing_license = db.get_server_license_key(target_server_id)
    if existing_license:
        embed = format_license_embed(
            "⚠️ Target Server Already Licensed",
            f"The target server **{target_guild.name}** already has an active license key.",
            discord.Color.orange()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return
    
    # Transfer the key
    success = db.transfer_key_to_server(license_key, interaction.user.id, target_server_id)
    
    if success:
        embed = format_license_embed(
            "✅ License Key Transferred",
            f"Successfully transferred license key to **{target_guild.name}**",
            discord.Color.green()
        )
        logger.info(f"License key transferred by {interaction.user} to server {target_guild.name} ({target_server_id})")
    else:
        embed = format_license_embed(
            "❌ Transfer Failed",
            "Failed to transfer the license key. Please try again.",
            discord.Color.red()
        )
    
    await interaction.followup.send(embed=embed, ephemeral=True)

@bot.tree.command(name="transfer-key-user", description="Transfer ownership of your license key to another user")
@require_key_owner(db)
async def transfer_key_user(interaction: discord.Interaction, license_key: str, user_id: str):
    """Transfer ownership of a license key to another user"""
    await interaction.response.defer(ephemeral=True)
    
    # Validate user ID
    target_user_id = validate_discord_id(user_id)
    if not target_user_id:
        await interaction.followup.send("❌ Invalid user ID format.", ephemeral=True)
        return
    
    if target_user_id == interaction.user.id:
        await interaction.followup.send("❌ You cannot transfer a key to yourself.", ephemeral=True)
        return
    
    # Try to get the target user
    try:
        target_user = await bot.fetch_user(target_user_id)
    except discord.NotFound:
        await interaction.followup.send("❌ Target user not found.", ephemeral=True)
        return
    
    # Transfer the key ownership
    success = db.transfer_key_to_user(license_key, interaction.user.id, target_user_id)
    
    if success:
        embed = format_license_embed(
            "✅ License Key Ownership Transferred",
            f"Successfully transferred ownership of license key to **{target_user.display_name}**\n"
            f"They now have full control over this license key.",
            discord.Color.green()
        )
    else:
        embed = format_license_embed(
            "❌ Transfer Failed",
            "Failed to transfer license key ownership. Please try again.",
            discord.Color.red()
        )
    
    await interaction.followup.send(embed=embed, ephemeral=True)

# ========== SERVER CONFIGURATION COMMANDS ==========

@bot.tree.command(name="rep", description="Set up server repping system with trigger word, role, and notification channel")
@app_commands.describe(
    trigger_word="The word to look for in user statuses",
    role="The role to assign to users with the trigger word in their status",
    channel="The channel where role assignment notifications will be sent"
)
@require_license(db)
async def rep(interaction: discord.Interaction, trigger_word: str, role: discord.Role, channel: discord.TextChannel):
    """Set up the server repping system with trigger word and role"""
    if not is_server_owner(interaction.user, interaction.guild):
        await interaction.response.send_message("❌ Only the server owner can configure the bot.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    # Validate trigger word
    if len(trigger_word.strip()) == 0:
        embed = format_license_embed(
            "❌ Invalid Trigger Word",
            "Trigger word cannot be empty.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    if len(trigger_word) > 50:
        embed = format_license_embed(
            "❌ Invalid Trigger Word",
            "Trigger word must be 50 characters or less.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    server_id = interaction.guild.id

    # Update trigger word, role, and channel in one operation
    config_update = {
        'trigger_word': trigger_word.strip(),
        'role_id': role.id,
        'channel_id': channel.id
    }

    success = db.save_server_config(server_id, config_update)

    if success:
        embed = format_license_embed(
            "✅ Server Repping System Configured",
            f"**Trigger Word:** `{trigger_word.strip()}`\n"
            f"**Role:** {role.mention}\n"
            f"**Notification Channel:** {channel.mention}\n\n"
            "Users with this word in their custom status will now receive the specified role, and notifications will be sent to the configured channel.",
            discord.Color.green()
        )
        logger.info(f"Rep system configured for server {interaction.guild.name} ({server_id}): trigger='{trigger_word.strip()}', role={role.name}")
    else:
        embed = format_license_embed(
            "❌ Configuration Error",
            "Failed to save repping system configuration.",
            discord.Color.red()
        )

    await interaction.followup.send(embed=embed, ephemeral=True)


@bot.tree.command(name="set-log-id", description="Set the channel for detailed logging")
@require_license(db)
async def set_log_id(interaction: discord.Interaction, channel: discord.TextChannel):
    """Set the log channel for the server"""
    if not is_server_owner(interaction.user, interaction.guild):
        await interaction.response.send_message("❌ Only the server owner can configure the bot.", ephemeral=True)
        return
    
    success = db.update_server_config_field(interaction.guild.id, 'log_channel_id', channel.id)
    
    if success:
        embed = format_license_embed(
            "✅ Log Channel Configuration Updated",
            f"Log channel set to: {channel.mention}\n"
            "Detailed role removal logs will be sent here.",
            discord.Color.green()
        )
    else:
        embed = format_license_embed(
            "❌ Configuration Failed",
            "Failed to update log channel configuration.",
            discord.Color.red()
        )
    
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="add-ignored-user", description="Add a user to the ignored list")
@require_license(db)
@require_configuration(db)
async def add_ignored_user(interaction: discord.Interaction, user: discord.Member):
    """Add a user to the ignored list"""
    if not is_server_owner(interaction.user, interaction.guild):
        await interaction.response.send_message("❌ Only the server owner can configure the bot.", ephemeral=True)
        return
    
    await interaction.response.defer(ephemeral=True)
    
    server_id = interaction.guild.id
    config = db.get_server_config(server_id)
    ignored_users = config.get('ignored_users', [])
    
    if user.id not in ignored_users:
        ignored_users.append(user.id)
        db.update_server_config_field(server_id, 'ignored_users', ignored_users)
        
        embed = format_license_embed(
            "✅ User Added to Ignore List",
            f"**{user.display_name}** has been added to the ignored users list.",
            discord.Color.green()
        )
    else:
        embed = format_license_embed(
            "⚠️ User Already Ignored",
            f"**{user.display_name}** is already in the ignored users list.",
            discord.Color.orange()
        )
    
    await interaction.followup.send(embed=embed, ephemeral=True)



@bot.tree.command(name="server-status", description="Check server configuration and license status")
@require_license(db)
async def server_status(interaction: discord.Interaction):
    """Check server license and configuration status"""
    server_id = interaction.guild.id
    license_key = db.get_server_license_key(server_id)
    config = db.get_server_config(server_id)
    
    embed = discord.Embed(
        title=f"🔍 Server Status - {interaction.guild.name}",
        color=discord.Color.blue()
    )
    
    # License information
    if license_key:
        embed.add_field(
            name="🔑 License Status",
            value=f"✅ **Active**\nKey: `{license_key['key'][:8]}...`\nRedeemed by: <@{license_key['redeemed_by']}>",
            inline=False
        )
    
    # Configuration status
    if config:
        role_id = config.get('role_id')
        channel_id = config.get('channel_id')
        log_channel_id = config.get('log_channel_id')
        ignored_users = config.get('ignored_users', [])
        trigger_word = config.get('trigger_word')
        
        config_status = []
        config_status.append(f"Role: {f'<@&{role_id}>' if role_id else '❌ Not set'}")
        config_status.append(f"Channel: {f'<#{channel_id}>' if channel_id else '❌ Not set'}")
        config_status.append(f"Log Channel: {f'<#{log_channel_id}>' if log_channel_id else '❌ Not set'}")
        config_status.append(f"Trigger Word: {f'`{trigger_word}`' if trigger_word else '❌ Not set'}")
        config_status.append(f"Ignored Users: {len(ignored_users)}")
        
        embed.add_field(
            name="⚙️ Configuration",
            value="\n".join(config_status),
            inline=False
        )
        
        # Check if fully configured
        is_configured, missing = db.is_server_configured(server_id)
        if is_configured:
            embed.add_field(name="Status", value="✅ Fully Configured", inline=True)
        else:
            embed.add_field(name="Status", value=f"⚠️ Missing: {', '.join(missing)}", inline=True)
            
        # Show current role count
        if role_id:
            role = interaction.guild.get_role(role_id)
            if role:
                embed.add_field(name="Current Role Members", value=f"{len(role.members)} users", inline=True)

    # Vent system status
    vent_settings = db.get_vent_settings(server_id)
    if vent_settings:
        vent_channel_id = vent_settings.get('vent_channel_id')
        vent_status = f"Vent Channel: {f'<#{vent_channel_id}>' if vent_channel_id else '❌ Not set'}"
        embed.add_field(
            name="💭 Vent System",
            value=vent_status,
            inline=False
        )

    # Sticky message system status
    sticky_messages = db.get_all_sticky_messages(server_id)
    if sticky_messages:
        sticky_channels = []
        for sticky in sticky_messages:
            channel_id = sticky.get('channel_id')
            if channel_id:
                sticky_channels.append(f"<#{channel_id}>")

        if sticky_channels:
            sticky_status = f"Active in {len(sticky_channels)} channel(s):\n" + "\n".join(sticky_channels[:5])
            if len(sticky_channels) > 5:
                sticky_status += f"\n... and {len(sticky_channels) - 5} more"
        else:
            sticky_status = "❌ No active sticky messages"

        embed.add_field(
            name="📌 Sticky Messages",
            value=sticky_status,
            inline=False
        )

    # DM Support system status
    dm_support_settings = db.get_dm_support_settings(server_id)
    if dm_support_settings:
        category_id = dm_support_settings.get('category_id')
        support_role_id = dm_support_settings.get('support_role_id')
        logs_channel_id = dm_support_settings.get('logs_channel_id')

        dm_status = []
        dm_status.append(f"Category: {f'<#{category_id}>' if category_id else '❌ Not set'}")
        dm_status.append(f"Support Role: {f'<@&{support_role_id}>' if support_role_id else '❌ Not set'}")
        dm_status.append(f"Logs Channel: {f'<#{logs_channel_id}>' if logs_channel_id else '❌ Not set'}")

        embed.add_field(
            name="🎫 DM Support System",
            value="\n".join(dm_status),
            inline=False
        )

    embed.set_footer(text="Leakin Bot - Multi-Server License System")
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.event
async def on_command_error(ctx, error):
    """Handle command errors"""
    if isinstance(error, commands.CommandNotFound):
        return  # Ignore unknown commands
    logger.error(f"Command error: {error}")

@bot.event
async def on_guild_join(guild):
    """Handle bot joining a new guild"""
    logger.info(f"Bot joined guild: {guild.name} ({guild.id})")

@bot.event
async def on_guild_remove(guild):
    """Handle bot leaving a guild"""
    logger.info(f"Bot left guild: {guild.name} ({guild.id})")
    # Clean up server tracking
    if guild.id in server_users_with_role:
        del server_users_with_role[guild.id]

@bot.event
async def on_message(message):
    """Handle DM messages for support tickets and support channel responses"""
    # Log all messages for debugging
    logger.info(f"Received message from {message.author} in {type(message.channel).__name__}: {message.content[:50]}")

    # Ignore messages from bots
    if message.author.bot:
        logger.debug(f"Ignoring bot message from {message.author}")
        return

    # Handle DMs
    if isinstance(message.channel, discord.DMChannel):
        logger.info(f"Received DM from {message.author} ({message.author.id}): {message.content[:100]}")

        try:
            # Check if user already has an open ticket
            existing_ticket = db.get_user_open_dm_ticket(message.author.id)
            if existing_ticket:
                logger.info(f"User {message.author.id} has existing ticket, forwarding message")
                # Add message to existing ticket and forward to support channel
                await handle_dm_message_to_ticket(message, existing_ticket)
                return

            # Start new ticket process
            logger.info(f"Starting new ticket process for user {message.author.id}")
            await start_dm_support_process(message)
        except Exception as e:
            logger.error(f"Error handling DM from {message.author.id}: {e}", exc_info=True)
            try:
                await message.channel.send("❌ An error occurred while processing your message. Please try again later.")
            except:
                pass
        return

    # Handle support channel messages
    if isinstance(message.channel, discord.TextChannel):
        # Check if this is a support channel
        ticket = db.get_dm_ticket_by_channel(message.channel.id)
        if ticket:
            # Handle close command
            if message.content.startswith('=close '):
                await handle_close_ticket_command(message, ticket)
                return

            # Forward message to user DM
            await handle_support_response(message, ticket)
            return

    # Process other commands
    await bot.process_commands(message)

async def start_dm_support_process(message):
    """Start the DM support ticket creation process"""
    user = message.author
    logger.info(f"Starting DM support process for user {user.id} ({user})")

    try:
        # Get all servers where user is a member and DM support is enabled
        user_servers = []
        for guild in bot.guilds:
            if guild.get_member(user.id):  # User is in this server
                logger.debug(f"User {user.id} is in guild {guild.name} ({guild.id})")
                dm_settings = db.get_dm_support_settings(guild.id)
                if dm_settings:
                    logger.debug(f"Guild {guild.id} has DM support settings")
                    if db.is_server_licensed(guild.id):
                        logger.debug(f"Guild {guild.id} is licensed, adding to user_servers")
                        user_servers.append({
                            'guild': guild,
                            'settings': dm_settings
                        })
                    else:
                        logger.debug(f"Guild {guild.id} is not licensed")
                else:
                    logger.debug(f"Guild {guild.id} has no DM support settings")

        logger.info(f"Found {len(user_servers)} servers with DM support for user {user.id}")

        if not user_servers:
            # Check if user is in any servers with the bot at all
            user_in_any_server = False
            all_user_servers = []
            licensed_servers = []
            unlicensed_servers = []

            for guild in bot.guilds:
                if guild.get_member(user.id):  # User is in this server
                    user_in_any_server = True
                    all_user_servers.append(guild.name)
                    if db.is_server_licensed(guild.id):
                        licensed_servers.append(guild.name)
                    else:
                        unlicensed_servers.append(guild.name)

            if not user_in_any_server:
                embed = discord.Embed(
                    title="❌ No Mutual Servers",
                    description="You're not in any servers where I'm present. Please join a server with the Leakin bot to create support tickets.",
                    color=discord.Color.red()
                )
            elif not licensed_servers:
                # User is in servers but none are licensed
                embed = discord.Embed(
                    title="❌ No Licensed Servers",
                    description=f"You're in {len(all_user_servers)} server(s) with me, but none have valid licenses.\n\n"
                               f"**Servers you're in:**\n" +
                               "\n".join([f"• {name}" for name in all_user_servers[:10]]) +
                               (f"\n... and {len(all_user_servers) - 10} more" if len(all_user_servers) > 10 else "") +
                               "\n\nDM support requires a server license. Ask a server administrator to redeem a license key.",
                    color=discord.Color.red()
                )
            else:
                # User is in licensed servers but none have DM support configured
                embed = discord.Embed(
                    title="❌ No DM Support Configured",
                    description=f"You're in {len(licensed_servers)} licensed server(s) with me, but none have DM support enabled.\n\n"
                               f"**Licensed servers you're in:**\n" +
                               "\n".join([f"• {name}" for name in licensed_servers[:10]]) +
                               (f"\n... and {len(licensed_servers) - 10} more" if len(licensed_servers) > 10 else "") +
                               "\n\nAsk a server administrator to set up DM support using `/dm-support-setup`.",
                    color=discord.Color.orange()
                )

            await message.channel.send(embed=embed)
            return

        if len(user_servers) == 1:
            # Only one server, proceed directly
            await create_server_selection_embed(message, user_servers[0]['guild'], message.content)
        else:
            # Multiple servers, check if message matches a server name
            message_lower = message.content.lower().strip()
            matched_server = None

            for server_data in user_servers:
                if server_data['guild'].name.lower() == message_lower:
                    matched_server = server_data['guild']
                    break

            if matched_server:
                # Server name matched, proceed to confirmation
                await create_server_selection_embed(message, matched_server, message.content)
            else:
                # Show server selection
                embed = discord.Embed(
                    title="🎫 Create Support Ticket",
                    description=f"Please type the name of the server you want to create a ticket for:\n\n" +
                               "\n".join([f"• **{server['guild'].name}**" for server in user_servers]),
                    color=discord.Color.blue()
                )
                embed.set_footer(text="Type the exact server name to continue")
                await message.channel.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in start_dm_support_process for user {user.id}: {e}", exc_info=True)
        try:
            await message.channel.send("❌ An error occurred while processing your request. Please try again later.")
        except:
            pass

async def create_server_selection_embed(message, guild, initial_message):
    """Create confirmation embed for server selection"""
    embed = discord.Embed(
        title="🎫 Confirm Support Ticket",
        description=f"Do you want to create a support ticket for **{guild.name}**?\n\n"
                   f"**Your message:**\n{initial_message[:500]}{'...' if len(initial_message) > 500 else ''}",
        color=discord.Color.orange()
    )
    embed.set_footer(text="Click Confirm to create the ticket or Cancel to abort")

    view = DMSupportConfirmView(guild.id, initial_message)
    await message.channel.send(embed=embed, view=view)

async def handle_dm_message_to_ticket(message, ticket):
    """Handle DM message for existing ticket"""
    # Add message to ticket database
    db.add_message_to_dm_ticket(
        str(ticket['_id']),
        message.author.id,
        str(message.author),
        message.content,
        is_staff=False
    )

    # Get the support channel
    guild = bot.get_guild(ticket['server_id'])
    if not guild:
        return

    channel = guild.get_channel(ticket['channel_id'])
    if not channel:
        return

    # Send message to support channel
    embed = discord.Embed(
        title="💬 User Message",
        description=message.content,
        color=discord.Color.blue(),
        timestamp=message.created_at
    )
    embed.set_author(
        name=str(message.author),
        icon_url=message.author.display_avatar.url
    )
    embed.set_footer(text=f"User ID: {message.author.id}")

    await channel.send(embed=embed)

    # Confirm to user
    await message.channel.send("✅ Your message has been sent to the support team.")

async def handle_support_response(message, ticket):
    """Handle support team response and forward to user DM"""
    # Get DM support settings to check support role
    dm_settings = db.get_dm_support_settings(ticket['server_id'])
    if not dm_settings:
        return

    # Check if user has permission to respond (support role or manage messages)
    support_role_id = dm_settings['support_role_id']
    is_staff = any(role.id == support_role_id for role in message.author.roles) or \
               message.author.guild_permissions.manage_messages

    if not is_staff:
        return  # Ignore messages from non-staff

    # Add message to ticket database
    db.add_message_to_dm_ticket(
        str(ticket['_id']),
        message.author.id,
        str(message.author),
        message.content,
        is_staff=True
    )

    # Get user and send DM
    user = bot.get_user(ticket['user_id'])
    if not user:
        try:
            user = await bot.fetch_user(ticket['user_id'])
        except discord.NotFound:
            await message.channel.send("❌ Could not find the user to send the response.")
            return

    # Send response to user
    guild = bot.get_guild(ticket['server_id'])
    embed = discord.Embed(
        title=f"💬 Support Response from {guild.name}",
        description=message.content,
        color=discord.Color.green(),
        timestamp=message.created_at
    )
    embed.set_author(
        name=str(message.author),
        icon_url=message.author.display_avatar.url
    )
    embed.set_footer(text=f"Staff ID: {message.author.id}")

    try:
        await user.send(embed=embed)
        await message.add_reaction("✅")  # Confirm message was sent
    except discord.Forbidden:
        await message.channel.send("❌ Could not send DM to user. They may have DMs disabled.")

async def handle_close_ticket_command(message, ticket):
    """Handle =close command in support channels"""
    # Check if user has permission to close tickets
    dm_settings = db.get_dm_support_settings(ticket['server_id'])
    if not dm_settings:
        return

    support_role = message.guild.get_role(dm_settings['support_role_id'])
    is_staff = support_role in message.author.roles or message.author.guild_permissions.manage_messages
    is_ticket_owner = message.author.id == ticket['user_id']

    if not (is_staff or is_ticket_owner):
        await message.channel.send("❌ You don't have permission to close this ticket.")
        return

    # Extract reason from command
    reason = message.content[7:].strip()  # Remove "=close "
    if not reason:
        await message.channel.send("❌ You must provide a reason for closing the ticket. Usage: `=close <reason>`")
        return

    # Close the ticket
    success = db.close_dm_support_ticket(str(ticket['_id']), message.author.id, reason)
    if not success:
        await message.channel.send("❌ Failed to close the ticket.")
        return

    # Create transcript
    transcript = db.get_dm_ticket_transcript(str(ticket['_id']))
    if transcript:
        await create_ticket_transcript(message.guild, transcript, dm_settings)

    # Notify user
    user = bot.get_user(ticket['user_id'])
    if user:
        try:
            embed = discord.Embed(
                title="🎫 Support Ticket Closed",
                description=f"Your support ticket in **{message.guild.name}** has been closed.\n\n"
                           f"**Reason:** {reason}\n"
                           f"**Closed by:** {message.author}",
                color=discord.Color.red()
            )
            await user.send(embed=embed)
        except discord.Forbidden:
            pass  # User has DMs disabled

    # Delete channel after 5 seconds
    await message.channel.send(f"🎫 Ticket closed by {message.author.mention}. Channel will be deleted in 5 seconds...")
    await asyncio.sleep(5)
    try:
        await message.channel.delete()
    except discord.NotFound:
        pass  # Channel already deleted

async def create_ticket_transcript(guild, ticket, dm_settings):
    """Create and send ticket transcript to logs channel"""
    logs_channel = guild.get_channel(dm_settings['logs_channel_id'])
    if not logs_channel:
        return

    # Create transcript content
    user = guild.get_member(ticket['user_id']) or f"Unknown User (ID: {ticket['user_id']})"
    closed_by = guild.get_member(ticket['closed_by']) or f"Unknown User (ID: {ticket['closed_by']})"

    # Calculate duration
    created_at = ticket['created_at']
    closed_at = ticket['closed_at']
    if created_at.tzinfo is None:
        created_at = created_at.replace(tzinfo=timezone.utc)
    if closed_at.tzinfo is None:
        closed_at = closed_at.replace(tzinfo=timezone.utc)

    duration = closed_at - created_at
    hours, remainder = divmod(int(duration.total_seconds()), 3600)
    minutes, seconds = divmod(remainder, 60)
    duration_str = f"{hours}h {minutes}m {seconds}s"

    # Create embed
    embed = discord.Embed(
        title="🎫 DM Support Ticket Transcript",
        description=f"**User:** {user}\n"
                   f"**Closed by:** {closed_by}\n"
                   f"**Reason:** {ticket['close_reason']}\n"
                   f"**Duration:** {duration_str}",
        color=discord.Color.blue(),
        timestamp=closed_at
    )

    # Add initial message
    embed.add_field(
        name="📝 Initial Message",
        value=ticket['initial_message'][:1024],
        inline=False
    )

    # Create transcript file
    transcript_content = f"DM Support Ticket Transcript\n"
    transcript_content += f"Server: {guild.name}\n"
    transcript_content += f"User: {user}\n"
    transcript_content += f"Created: {created_at}\n"
    transcript_content += f"Closed: {closed_at}\n"
    transcript_content += f"Closed by: {closed_by}\n"
    transcript_content += f"Reason: {ticket['close_reason']}\n"
    transcript_content += f"Duration: {duration_str}\n\n"
    transcript_content += "=" * 50 + "\n\n"

    transcript_content += f"Initial Message:\n{ticket['initial_message']}\n\n"

    # Add all messages
    for msg in ticket.get('messages', []):
        timestamp = msg['timestamp']
        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=timezone.utc)

        role = "STAFF" if msg['is_staff'] else "USER"
        transcript_content += f"[{timestamp.strftime('%Y-%m-%d %H:%M:%S')}] {role} - {msg['username']}:\n"
        transcript_content += f"{msg['message']}\n\n"

    # Send transcript
    file = discord.File(
        io.StringIO(transcript_content),
        filename=f"ticket-{ticket['user_id']}-{int(closed_at.timestamp())}.txt"
    )

    await logs_channel.send(embed=embed, file=file)

class DMSupportConfirmView(ui.View):
    def __init__(self, guild_id: int, initial_message: str):
        super().__init__(timeout=300)  # 5 minute timeout
        self.guild_id = guild_id
        self.initial_message = initial_message

    @ui.button(emoji="✅", style=ButtonStyle.success, custom_id="dm_support_confirm")
    async def confirm_ticket(self, interaction: discord.Interaction, button: ui.Button):
        # Create the ticket
        ticket_id = db.create_dm_support_ticket(
            self.guild_id,
            interaction.user.id,
            self.initial_message
        )

        if ticket_id == "existing":
            await interaction.response.send_message("❌ You already have an open support ticket.", ephemeral=True)
            return

        # Get guild and settings
        guild = bot.get_guild(self.guild_id)
        if not guild:
            await interaction.response.send_message("❌ Server not found.", ephemeral=True)
            return

        dm_settings = db.get_dm_support_settings(self.guild_id)
        if not dm_settings:
            await interaction.response.send_message("❌ DM support is not configured for this server.", ephemeral=True)
            return

        # Create support channel
        await create_support_channel(interaction, guild, dm_settings, ticket_id)

    @ui.button(emoji="❌", style=ButtonStyle.danger, custom_id="dm_support_cancel")
    async def cancel_ticket(self, interaction: discord.Interaction, button: ui.Button):
        embed = discord.Embed(
            title="❌ Ticket Cancelled",
            description="Support ticket creation has been cancelled.",
            color=discord.Color.red()
        )
        await interaction.response.edit_message(embed=embed, view=None)

async def create_support_channel(interaction, guild, dm_settings, ticket_id):
    """Create the support channel for the ticket"""
    try:
        category = guild.get_channel(dm_settings['category_id'])
        support_role = guild.get_role(dm_settings['support_role_id'])

        if not category or not support_role:
            await interaction.response.send_message("❌ Server configuration is invalid.", ephemeral=True)
            return

        # Create channel
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(read_messages=False),
            support_role: discord.PermissionOverwrite(
                read_messages=True,
                send_messages=True,
                manage_messages=True
            ),
            guild.me: discord.PermissionOverwrite(
                read_messages=True,
                send_messages=True,
                manage_channels=True
            )
        }

        channel_name = f"dm-{interaction.user.name}"
        channel = await guild.create_text_channel(
            name=channel_name[:100],
            category=category,
            overwrites=overwrites,
            topic=f"DM Support ticket for {interaction.user} (ID: {interaction.user.id})"
        )

        # Update ticket with channel ID
        db.update_dm_ticket_channel(ticket_id, channel.id)

        # Send initial message
        embed = discord.Embed(
            title="🎫 New DM Support Ticket",
            description=f"**User:** {interaction.user.mention} ({interaction.user})\n"
                       f"**User ID:** {interaction.user.id}\n\n"
                       f"**Initial Message:**\n{db.get_dm_ticket_transcript(ticket_id)['initial_message']}",
            color=discord.Color.green(),
            timestamp=datetime.now(timezone.utc)
        )
        embed.set_author(
            name=str(interaction.user),
            icon_url=interaction.user.display_avatar.url
        )
        embed.set_footer(text="Type =close <reason> to close this ticket")

        await channel.send(f"{support_role.mention}", embed=embed)

        # Confirm to user
        embed = discord.Embed(
            title="✅ Support Ticket Created",
            description=f"Your support ticket has been created in **{guild.name}**.\n"
                       "The support team will respond to you here via DM.",
            color=discord.Color.green()
        )
        await interaction.response.edit_message(embed=embed, view=None)

    except Exception as e:
        logger.error(f"Error creating support channel: {e}")
        await interaction.response.send_message("❌ An error occurred while creating your ticket.", ephemeral=True)

# Helper command for admins to view license keys
@bot.tree.command(name="my-keys", description="View your license keys")
async def my_keys(interaction: discord.Interaction):
    """View user's license keys"""
    user_id = interaction.user.id
    user_keys = db.get_user_license_keys(user_id)
    
    if not user_keys:
        embed = format_license_embed(
            "📝 Your License Keys",
            "You don't have any license keys.",
            discord.Color.blue()
        )
    else:
        embed = discord.Embed(
            title="📝 Your License Keys",
            color=discord.Color.blue()
        )
        
        for i, key_data in enumerate(user_keys, 1):
            server_name = "Unknown Server"
            if key_data['server_id']:
                guild = bot.get_guild(key_data['server_id'])
                if guild:
                    server_name = guild.name
            
            embed.add_field(
                name=f"Key #{i}",
                value=f"Key: `{key_data['key'][:8]}...`\n"
                      f"Server: {server_name}\n"
                      f"Redeemed: {key_data['redeemed_at'].strftime('%Y-%m-%d') if key_data['redeemed_at'] else 'N/A'}",
                inline=True
            )
        
        embed.set_footer(text="Leakin Bot - Multi-Server License System")
    
    await interaction.response.send_message(embed=embed, ephemeral=True)

# Debug command to manually check status detection
@bot.tree.command(name="debug-status", description="Debug status detection for a user")
async def debug_status(interaction: discord.Interaction, user: discord.Member = None):
    """Debug status detection"""
    if interaction.user.id != ADMIN_USER_ID:
        await interaction.response.send_message("❌ Access denied.", ephemeral=True)
        return
    
    target_user = user or interaction.user
    await interaction.response.defer(ephemeral=True)
    
    # Check user's current status
    status_text = ""
    activities_info = []
    
    for activity in target_user.activities:
        activity_info = f"Type: {activity.type}, Name: {getattr(activity, 'name', 'None')}, State: {getattr(activity, 'state', 'None')}"
        activities_info.append(activity_info)
        
        if activity.type == discord.ActivityType.custom:
            if hasattr(activity, 'name') and activity.name:
                status_text = activity.name
                break
            elif hasattr(activity, 'state') and activity.state:
                status_text = activity.state
                break
    
    # Get server trigger word
    config = db.get_server_config(interaction.guild.id)
    trigger_word = config.get('trigger_word') if config else None
    
    if not trigger_word:
        embed = discord.Embed(
            title="🔍 Status Debug",
            description="❌ No trigger word configured for this server.\nUse `/rep <trigger-word> <role>` to set up the repping system.",
            color=discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return
    
    trigger_lower = trigger_word.lower()
    status_lower = status_text.lower() if status_text else ""
    has_trigger = trigger_lower in status_lower if status_text else False
    
    embed = discord.Embed(
        title="🔍 Status Debug",
        description=f"**User:** {target_user.display_name}\n**Status:** {target_user.status}",
        color=discord.Color.blue()
    )
    
    embed.add_field(name="Custom Status Text", value=f"`{status_text}`" if status_text else "*None*", inline=False)
    embed.add_field(name="Trigger Word", value=f"`{trigger_word}`", inline=True)
    embed.add_field(name="Contains Trigger", value="✅ Yes" if has_trigger else "❌ No", inline=True)
    
    if activities_info:
        embed.add_field(name="All Activities", value="\n".join(activities_info[:5]), inline=False)
    
    await interaction.followup.send(embed=embed, ephemeral=True)

# Admin command to check system status
@bot.tree.command(name="system-status", description="Check system status (Admin only)")
async def system_status(interaction: discord.Interaction):
    """Check system status (Admin only)"""
    if interaction.user.id != ADMIN_USER_ID:
        await interaction.response.send_message("❌ Access denied.", ephemeral=True)
        return
    
    # Count licensed servers
    licensed_servers = 0
    configured_servers = 0
    
    for guild in bot.guilds:
        if db.is_server_licensed(guild.id):
            licensed_servers += 1
            is_configured, _ = db.is_server_configured(guild.id)
            if is_configured:
                configured_servers += 1
    
    embed = discord.Embed(
        title="🔧 System Status",
        color=discord.Color.green()
    )
    
    embed.add_field(name="Total Servers", value=f"{len(bot.guilds)}", inline=True)
    embed.add_field(name="Licensed Servers", value=f"{licensed_servers}", inline=True)
    embed.add_field(name="Configured Servers", value=f"{configured_servers}", inline=True)
    
    # Show active role assignments
    total_active_users = sum(len(users) for users in server_users_with_role.values())
    embed.add_field(name="Active Role Assignments", value=f"{total_active_users}", inline=True)
    
    embed.set_footer(text="Leakin Bot - System Administrator")
    await interaction.response.send_message(embed=embed, ephemeral=True)

# Admin command to disable a license key
@bot.tree.command(name="disable-key", description="Disable a license key and all its features (Admin only)")
async def disable_key(interaction: discord.Interaction, key: str):
    """Disable a license key (Admin only)"""
    if interaction.user.id != ADMIN_USER_ID:
        await interaction.response.send_message("❌ Access denied.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    # Check if key exists
    key_info = db.get_license_key_by_key(key)
    if not key_info:
        embed = format_license_embed(
            "❌ Key Not Found",
            f"License key `{key}` does not exist in the database.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    # Disable the key
    success = db.disable_license_key(key)

    if success:
        embed = format_license_embed(
            "✅ License Key Disabled",
            f"License key `{key}` has been disabled.\n\n"
            f"**Key Information:**\n"
            f"• Redeemed: {'Yes' if key_info.get('redeemed') else 'No'}\n"
            f"• Server ID: {key_info.get('server_id', 'None')}\n"
            f"• Redeemed by: <@{key_info.get('redeemed_by', 'None')}>\n\n"
            f"All features for this key are now disabled.",
            discord.Color.orange()
        )
        logger.info(f"License key {key} disabled by admin {interaction.user} ({interaction.user.id})")
    else:
        embed = format_license_embed(
            "❌ Failed to Disable Key",
            f"Failed to disable license key `{key}`. Please try again.",
            discord.Color.red()
        )

    await interaction.followup.send(embed=embed, ephemeral=True)

# Admin command to enable a license key
@bot.tree.command(name="enable-key", description="Enable a license key (Admin only)")
async def enable_key(interaction: discord.Interaction, key: str):
    """Enable a license key (Admin only)"""
    if interaction.user.id != ADMIN_USER_ID:
        await interaction.response.send_message("❌ Access denied.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    # Check if key exists
    key_info = db.get_license_key_by_key(key)
    if not key_info:
        embed = format_license_embed(
            "❌ Key Not Found",
            f"License key `{key}` does not exist in the database.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    # Enable the key
    success = db.enable_license_key(key)

    if success:
        embed = format_license_embed(
            "✅ License Key Enabled",
            f"License key `{key}` has been enabled.\n\n"
            f"**Key Information:**\n"
            f"• Redeemed: {'Yes' if key_info.get('redeemed') else 'No'}\n"
            f"• Server ID: {key_info.get('server_id', 'None')}\n"
            f"• Redeemed by: <@{key_info.get('redeemed_by', 'None')}>\n\n"
            f"All features for this key are now active.",
            discord.Color.green()
        )
        logger.info(f"License key {key} enabled by admin {interaction.user} ({interaction.user.id})")
    else:
        embed = format_license_embed(
            "❌ Failed to Enable Key",
            f"Failed to enable license key `{key}`. Please try again.",
            discord.Color.red()
        )

    await interaction.followup.send(embed=embed, ephemeral=True)

# Admin command to get server's license key
@bot.tree.command(name="server-key", description="Get the full license key for a server (Admin only)")
async def server_key(interaction: discord.Interaction, serverid: str):
    """Get server's license key (Admin only)"""
    if interaction.user.id != ADMIN_USER_ID:
        await interaction.response.send_message("❌ Access denied.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    # Validate server ID
    server_id = validate_discord_id(serverid)
    if not server_id:
        await interaction.followup.send("❌ Invalid server ID format.", ephemeral=True)
        return

    # Get the server's license key (including disabled ones)
    key_info = db.get_server_license_key_full(server_id)

    if not key_info:
        embed = format_license_embed(
            "❌ No License Key Found",
            f"No license key found for server ID `{server_id}`.\n\n"
            f"This server either:\n"
            f"• Has no license key redeemed\n"
            f"• The license key was transferred to another server",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    # Get server name if bot is in the server
    server_name = "Unknown Server"
    guild = bot.get_guild(server_id)
    if guild:
        server_name = guild.name

    # Format the key information
    disabled_status = key_info.get('disabled', False)
    status_color = discord.Color.red() if disabled_status else discord.Color.green()
    status_text = "🔴 DISABLED" if disabled_status else "🟢 ACTIVE"

    embed = format_license_embed(
        f"🔑 License Key for {server_name}",
        f"**Full License Key:** `{key_info['key']}`\n\n"
        f"**Status:** {status_text}\n"
        f"**Server ID:** {server_id}\n"
        f"**Redeemed by:** <@{key_info.get('redeemed_by', 'Unknown')}>\n"
        f"**Redeemed at:** {key_info.get('redeemed_at', 'Unknown')}\n"
        f"**Created at:** {key_info.get('created_at', 'Unknown')}",
        status_color
    )

    if disabled_status and key_info.get('disabled_at'):
        embed.add_field(
            name="Disabled Information",
            value=f"**Disabled at:** {key_info['disabled_at']}",
            inline=False
        )

    logger.info(f"Admin {interaction.user} ({interaction.user.id}) viewed license key for server {server_id}")
    await interaction.followup.send(embed=embed, ephemeral=True)

# Graceful shutdown handler
async def shutdown():
    """Gracefully shutdown the bot"""
    logger.info("Shutting down bot...")
    try:
        db.disconnect()
    except Exception as e:
        logger.error(f"Error disconnecting from database: {e}")
    
    await bot.close()

# ========== DM SUPPORT SYSTEM ==========

@bot.tree.command(name="dm-support-setup", description="Set up DM support system for this server")
@app_commands.describe(
    category="The category where support tickets will be created",
    support_role="The role that can respond to support tickets"
)
@require_license(db)
async def dm_support_setup(interaction: discord.Interaction, category: discord.CategoryChannel, support_role: discord.Role):
    """Set up DM support system"""
    if not is_server_owner(interaction.user, interaction.guild):
        await interaction.response.send_message("❌ Only the server owner can configure the bot.", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    # Check if logs channel is configured
    config = db.get_server_config(interaction.guild.id)
    if not config or not config.get('log_channel_id'):
        embed = format_license_embed(
            "❌ Configuration Required",
            "You must set up a logs channel first using `/set-log-id <channel>` before configuring DM support.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    logs_channel_id = config['log_channel_id']
    logs_channel = interaction.guild.get_channel(logs_channel_id)
    if not logs_channel:
        embed = format_license_embed(
            "❌ Invalid Logs Channel",
            "The configured logs channel is invalid. Please reconfigure it using `/set-log-id <channel>`.",
            discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
        return

    # Create support-logs channel in the category
    try:
        overwrites = {
            interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
            support_role: discord.PermissionOverwrite(
                read_messages=True,
                send_messages=True,
                read_message_history=True
            ),
            interaction.guild.me: discord.PermissionOverwrite(
                read_messages=True,
                send_messages=True,
                manage_channels=True
            )
        }

        support_logs_channel = await interaction.guild.create_text_channel(
            name="support-logs",
            category=category,
            overwrites=overwrites,
            topic="DM Support ticket logs and transcripts"
        )

        # Save DM support settings
        success = db.set_dm_support_settings(
            interaction.guild.id,
            category.id,
            support_role.id,
            support_logs_channel.id
        )

        if success:
            embed = format_license_embed(
                "✅ DM Support System Configured",
                f"**Category:** {category.mention}\n"
                f"**Support Role:** {support_role.mention}\n"
                f"**Support Logs Channel:** {support_logs_channel.mention}\n"
                f"**Main Logs Channel:** {logs_channel.mention}\n\n"
                "Users can now DM the bot to create support tickets for this server.",
                discord.Color.green()
            )
        else:
            embed = format_license_embed(
                "❌ Configuration Error",
                "Failed to save DM support configuration.",
                discord.Color.red()
            )

    except Exception as e:
        logger.error(f"Error setting up DM support: {e}")
        embed = format_license_embed(
            "❌ Setup Error",
            "An error occurred while setting up DM support. Please try again.",
            discord.Color.red()
        )

    await interaction.followup.send(embed=embed, ephemeral=True)

# Run the bot
# Gender Verification Views
class GenderVerificationView(ui.View):
    def __init__(self, db: DatabaseManager):
        super().__init__(timeout=None)
        self.db = db
    @ui.button(
        label="Create Ticket",
        emoji="🎟️",
        style=ButtonStyle.secondary,
        custom_id="create_gender_ticket"
    )
    async def create_ticket(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license first
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return
            
        # Check if user already has an open ticket
        existing_ticket = self.db.get_user_open_ticket(interaction.guild_id, interaction.user.id)
        if existing_ticket:
            await interaction.response.send_message("❌ You already have an open verification ticket.", ephemeral=True)
            return
            
        # Create a new ticket
        ticket_result = self.db.create_gender_verification_ticket(interaction.guild_id, interaction.user.id)
        
        if ticket_result == "existing":
            await interaction.response.send_message("❌ You already have an open verification ticket.", ephemeral=True)
            return
        elif ticket_result == "recent":
            await interaction.response.send_message("❌ You can only create a new ticket 12 hours after your last one was closed.", ephemeral=True)
            return
            
        # Get verification settings
        settings = self.db.get_gender_verification_settings(interaction.guild_id)
        if not settings:
            await interaction.response.send_message("❌ Gender verification is not properly configured on this server.", ephemeral=True)
            return
            
        # Create the ticket channel
        category = interaction.guild.get_channel(settings['category_id'])
        if not category:
            await interaction.response.send_message("❌ The ticket category is invalid.", ephemeral=True)
            return
            
        support_role = interaction.guild.get_role(settings['support_role_id'])
        if not support_role:
            await interaction.response.send_message("❌ The support role is invalid.", ephemeral=True)
            return
            
        # Create the ticket channel
        try:
            overwrites = {
                interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
                interaction.user: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    read_message_history=True
                ),
                support_role: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_messages=True
                ),
                interaction.guild.me: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_channels=True
                )
            }
            
            channel_name = f"gender-{interaction.user.name}"
            channel = await interaction.guild.create_text_channel(
                name=channel_name[:100],  # Ensure channel name is within Discord's limit
                category=category,
                overwrites=overwrites,
                topic=str(interaction.user.id)  # Store user ID in channel topic for reference
            )
            
            # Update the ticket with channel ID
            ticket_id = ticket_result
            self.db.db['leakin-gender-tickets'].update_one(
                {"_id": ObjectId(ticket_id)},
                {"$set": {"channel_id": channel.id}}
            )
            
            # Send welcome message
            embed = Embed(
                title="Read the instructions below:",
                description=(
                    f"Thanks {interaction.user.mention} for opening a ticket in {interaction.guild.name}!\n\n"
                    f"Please send a video of you holding up a piece of paper that says **{settings['paper_text']}** on it.\n\n"
                    "Additionally, please send a selfie you would like public for the selfies channel.\n\n"
                    "If you prefer to verify in a private VC, let us know and you can turn your camera on inside a voice channel."
                ),
                color=Color.blue()
            )
            embed.set_footer(text=".gg/leakin")
            
            close_view = CloseTicketView(self.db)
            
            message = await channel.send(
                content=f"{interaction.user.mention} {support_role.mention}", 
                embed=embed, 
                view=close_view
            )
            
            await interaction.response.send_message(f"✅ Ticket created: {channel.mention}", ephemeral=True)
            
        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            await interaction.response.send_message("❌ An error occurred while creating your ticket.", ephemeral=True)


class CloseTicketView(ui.View):
    def __init__(self, db: DatabaseManager):
        super().__init__(timeout=None)
        self.db = db

    @ui.button(label="Close Ticket", style=ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: Interaction, button: ui.Button):
        # Only allow ticket owner or staff to close
        is_staff = any(role.permissions.manage_messages for role in interaction.user.roles)
        is_owner = interaction.channel.topic == str(interaction.user.id)
        
        if not (is_staff or is_owner):
            await interaction.response.send_message("❌ You don't have permission to close this ticket.", ephemeral=True)
            return
        
        # Disable the close button to prevent multiple clicks
        button.disabled = True
        await interaction.response.edit_message(view=self)
        
        # Get the ticket from database
        ticket = self.db.db['leakin-gender-tickets'].find_one({
            "channel_id": interaction.channel.id,
            "status": "open"
        })
        
        if not ticket:
            await interaction.followup.send("❌ Could not find an open ticket for this channel.", ephemeral=True)
            return

        # Send closing message
        await interaction.followup.send("⏳ This ticket will be closed in 5 seconds...")
        
        # Get server config for log channel
        config = self.db.get_server_config(interaction.guild_id)
        if not config or not config.get('log_channel_id'):
            logger.warning("No log channel configured for ticket transcripts")
            # Continue with channel deletion even if no log channel
        else:
            log_channel = interaction.guild.get_channel(config['log_channel_id'])
            if not log_channel:
                logger.warning(f"Log channel {config['log_channel_id']} not found")
            else:
                try:
                    # Get ticket information with error handling
                    ticket = self.db.db['leakin-gender-tickets'].find_one({
                        "channel_id": interaction.channel.id,
                        "status": "open"
                    })
                    
                    if ticket:  # Only proceed if ticket is still open
                        # Get user who created the ticket
                        creator = interaction.guild.get_member(ticket['user_id'])
                        creator_name = str(creator) if creator else f"Unknown User (ID: {ticket['user_id']})"
                        
                        # Calculate ticket duration with timezone-aware datetimes
                        created_at = ticket.get('created_at')
                        if created_at and created_at.tzinfo is None:
                            created_at = created_at.replace(tzinfo=timezone.utc)
                        created_at = created_at or datetime.now(timezone.utc)
                        closed_at = datetime.now(timezone.utc)
                        duration = closed_at - created_at
                        
                        # Format duration
                        hours, remainder = divmod(int(duration.total_seconds()), 3600)
                        minutes, seconds = divmod(remainder, 60)
                        duration_str = f"{hours}h {minutes}m {seconds}s"
                        
                        # Create a beautiful embed for the log
                        embed = discord.Embed(
                            title="🎫 Ticket Closed",
                            description=f"**Ticket:** {interaction.channel.mention}\n"
                                      f"**Creator:** {creator_name}\n"
                                      f"**Closed by:** {interaction.user.mention}\n"
                                      f"**Duration:** {duration_str}",
                            color=discord.Color.green(),
                            timestamp=closed_at
                        )
                        
                        # Add fields with ticket details
                        embed.add_field(
                            name="📅 Timeline",
                            value=f"• Created: <t:{int(created_at.timestamp())}:f>\n"
                                  f"• Closed: <t:{int(closed_at.timestamp())}:f>",
                            inline=True
                        )
                        
                        # Get message count
                        message_count = len([m async for m in interaction.channel.history(limit=None)])
                        embed.add_field(
                            name="📊 Stats",
                            value=f"• Messages: {message_count}\n"
                                  f"• Participants: {len(interaction.channel.members)}",
                            inline=True
                        )
                        
                        # Add footer with ticket ID
                        embed.set_footer(text=f"Ticket ID: {ticket['_id']}")
                        
                        # Send the embed
                        await log_channel.send(embed=embed)
                        
                        # Generate HTML transcript
                        messages = []
                        async for message in interaction.channel.history(limit=None, oldest_first=True):
                            messages.append(message)
                        
                        # Create HTML transcript with improved styling
                        html_content = """
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset="UTF-8">
                            <title>Ticket Transcript</title>
                            <style>
                                body {
                                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                    line-height: 1.6;
                                    color: #dcddde;
                                    background-color: #36393f;
                                    margin: 0;
                                    padding: 20px;
                                }
                                .message {
                                    margin-bottom: 15px;
                                    padding: 10px;
                                    border-radius: 5px;
                                    background-color: #2f3136;
                                }
                                .author {
                                    font-weight: bold;
                                    color: #fff;
                                    margin-bottom: 5px;
                                }
                                .timestamp {
                                    color: #72767d;
                                    font-size: 0.8em;
                                    margin-left: 10px;
                                }
                                .content {
                                    margin-top: 5px;
                                }
                                .system {
                                    color: #43b581;
                                    font-style: italic;
                                }
                            </style>
                        </head>
                        <body>
                            <h1>Ticket Transcript</h1>
                            <div class="system">Ticket created by {creator_name}</div>
                            <div class="system">Closed by {closer_name}</div>
                            <div class="system">Duration: {duration_str}</div>
                            <div class="system">Total messages: {message_count}</div>
                            <hr>
                        """
                        
                        for message in messages:
            
                            # Format the message content with attachments if any
                            content = message.content
                            if message.attachments:
                                content += "\n" + "\n".join(f"[Attachment: {a.filename}]" for a in message.attachments)
            
                            # Escape HTML in the message content
                            content = content.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
            
                            # Add the message to the HTML
                            html_content += f"""
                            <div class="message">
                                <div class="author">
                                    {message.author.display_name}
                                    <span class="timestamp">{message.created_at.strftime('%Y-%m-%d %H:%M:%S')} UTC</span>
                                </div>
                                <div class="content">
                                    {content}
                                </div>
                            </div>
                            """
            
                        html_content += """
                        </body>
                        </html>
                        """.format(
                            creator_name=creator_name,
                            closer_name=interaction.user.display_name,
                            duration_str=duration_str,
                            message_count=message_count
                        )
            
                        # Create a file with the transcript
                        transcript_file = discord.File(
                            io.BytesIO(html_content.encode('utf-8')),
                            filename=f"transcript-{interaction.channel.name}-{int(closed_at.timestamp())}.html"
                        )
            
                        # Send the transcript file
                        await log_channel.send(file=transcript_file)
                except Exception as e:
                    logger.error(f"Error generating transcript: {e}", exc_info=True)
                    await log_channel.send("❌ An error occurred while generating the transcript.")
        
        # Wait 5 seconds before deleting the channel
        await asyncio.sleep(5)
        
        try:
            # Delete the channel
            channel_name = interaction.channel.name
            await interaction.channel.delete(reason=f"Ticket closed by {interaction.user}")
            logger.info(f"Successfully deleted ticket channel: {channel_name}")
            
            # Only mark as closed after successful channel deletion
            result = self.db.db['leakin-gender-tickets'].update_one(
                {"_id": ticket['_id']},
                {
                    "$set": {
                        "status": "closed",
                        "closed_at": datetime.now(timezone.utc),
                        "closed_by": interaction.user.id
                    }
                }
            )
            
            if result.modified_count == 0:
                logger.warning(f"Failed to update ticket status in database for channel {interaction.channel.id}")
                
        except Exception as e:
            logger.error(f"Error deleting ticket channel: {e}", exc_info=True)
            # If we couldn't delete the channel, still mark it as closed to prevent issues
            self.db.db['leakin-gender-tickets'].update_one(
                {"_id": ticket['_id']},
                {
                    "$set": {
                        "status": "closed",
                        "closed_at": datetime.now(timezone.utc),
                        "closed_by": interaction.user.id,
                        "error": f"Failed to delete channel: {str(e)}"
                    }
                }
            )
            raise  # Re-raise the exception after logging


# TempVoice Views
class TempVoiceView(ui.View):
    def __init__(self, db: DatabaseManager):
        super().__init__(timeout=None)
        self.db = db

    async def create_channel(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license first
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Check if user already has a temp channel
        existing_channel = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if existing_channel:
            channel = interaction.guild.get_channel(existing_channel['channel_id'])
            if channel:
                # Move user to their existing channel if they're in voice
                try:
                    if interaction.user.voice:
                        await interaction.user.move_to(channel)
                        await interaction.response.send_message(f"✅ Moved you to your existing channel: {channel.mention}", ephemeral=True)
                    else:
                        await interaction.response.send_message(f"✅ You already have a temporary voice channel: {channel.mention}", ephemeral=True)
                except discord.HTTPException:
                    await interaction.response.send_message(f"✅ You already have a temporary voice channel: {channel.mention}", ephemeral=True)
                return
            else:
                # Channel was deleted but not cleaned up in database
                self.db.delete_temp_channel(existing_channel['channel_id'])

        # Get TempVoice settings
        settings = self.db.get_tempvoice_settings(interaction.guild_id)
        if not settings:
            await interaction.response.send_message("❌ TempVoice is not properly configured on this server.", ephemeral=True)
            return

        # Get the creator channel to copy permissions from
        creator_channel = interaction.guild.get_channel(settings['creator_channel_id'])
        if not creator_channel or not isinstance(creator_channel, discord.VoiceChannel):
            await interaction.response.send_message("❌ Creator voice channel not found or invalid.", ephemeral=True)
            return

        await interaction.response.send_message(f"💡 **Tip:** Join {creator_channel.mention} to automatically create your temporary voice channel!\n\nAlternatively, I can create one for you now if you prefer.", ephemeral=True)

        # Create the temporary voice channel as fallback
        try:
            # Copy permissions from creator channel
            overwrites = creator_channel.overwrites.copy()

            # Ensure @everyone permissions are copied from creator channel
            if interaction.guild.default_role in creator_channel.overwrites:
                overwrites[interaction.guild.default_role] = creator_channel.overwrites[interaction.guild.default_role]

            # Get default user limit
            default_limit = settings.get('default_user_limit')

            channel_name = f"{interaction.user.display_name}'s Channel"
            temp_channel = await interaction.guild.create_voice_channel(
                name=channel_name[:100],  # Ensure channel name is within Discord's limit
                category=creator_channel.category,
                overwrites=overwrites,
                user_limit=default_limit  # Apply default user limit
            )

            # Save to database
            success = self.db.create_temp_channel(interaction.guild_id, interaction.user.id, temp_channel.id)
            if not success:
                await temp_channel.delete(reason="Failed to save to database")
                await interaction.followup.send("❌ Failed to create temporary channel.", ephemeral=True)
                return

            # Move user to their channel if they're in voice
            try:
                if interaction.user.voice:
                    await interaction.user.move_to(temp_channel)
            except discord.HTTPException:
                pass  # User might not be in a voice channel

            await interaction.followup.send(f"✅ Created your temporary voice channel: {temp_channel.mention}", ephemeral=True)

        except Exception as e:
            logger.error(f"Error creating temp voice channel: {e}")
            await interaction.followup.send("❌ An error occurred while creating your channel.", ephemeral=True)

    @ui.button(
        emoji="➕",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_set_limit"
    )
    async def set_user_limit(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        # Show modal for user limit input
        modal = UserLimitModal(self.db, temp_channel_data['channel_id'])
        await interaction.response.send_modal(modal)

    @ui.button(
        emoji="👢",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_kick_user"
    )
    async def kick_user(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "kick")
        await interaction.response.send_message("Select a user to kick from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="⛔",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_block_user"
    )
    async def block_user(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "block")
        await interaction.response.send_message("Select a user to block from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="✅",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_unblock_user"
    )
    async def unblock_user(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "unblock")
        await interaction.response.send_message("Select a user to unblock from your voice channel:", view=view, ephemeral=True)

    @ui.button(
        emoji="🔒",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_lock_channel"
    )
    async def lock_channel(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        try:
            # Lock the channel by denying connect permission to @everyone
            await channel.set_permissions(interaction.guild.default_role, connect=False)

            # Update database
            self.db.set_temp_channel_lock(temp_channel_data['channel_id'], True)

            await interaction.response.send_message("🔒 Your voice channel has been locked.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error locking temp voice channel: {e}")
            await interaction.response.send_message("❌ An error occurred while locking your channel.", ephemeral=True)

    @ui.button(
        emoji="🔓",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_unlock_channel"
    )
    async def unlock_channel(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        try:
            # Unlock the channel by allowing connect permission to @everyone
            await channel.set_permissions(interaction.guild.default_role, connect=None)

            # Update database
            self.db.set_temp_channel_lock(temp_channel_data['channel_id'], False)

            await interaction.response.send_message("🔓 Your voice channel has been unlocked.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error unlocking temp voice channel: {e}")
            await interaction.response.send_message("❌ An error occurred while unlocking your channel.", ephemeral=True)

    @ui.button(
        emoji="🔄",
        style=ButtonStyle.secondary,
        custom_id="tempvoice_transfer_ownership"
    )
    async def transfer_ownership(self, interaction: Interaction, button: ui.Button):
        # Check if server has a valid license
        if not self.db.is_server_licensed(interaction.guild_id):
            await interaction.response.send_message("❌ This server doesn't have an active license.", ephemeral=True)
            return

        # Get user's temp channel
        temp_channel_data = self.db.get_user_temp_channel(interaction.guild_id, interaction.user.id)
        if not temp_channel_data:
            await interaction.response.send_message("❌ You don't have a temporary voice channel.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(temp_channel_data['channel_id'])
        if not channel:
            await interaction.response.send_message("❌ Your temporary voice channel was not found.", ephemeral=True)
            return

        # Show user select menu
        view = UserSelectView(self.db, temp_channel_data['channel_id'], "transfer")
        await interaction.response.send_message("Select a user to transfer ownership to:", view=view, ephemeral=True)


# TempVoice User Select View
class UserSelectView(ui.View):
    def __init__(self, db: DatabaseManager, channel_id: int, action: str):
        super().__init__(timeout=60)
        self.db = db
        self.channel_id = channel_id
        self.action = action

        # Add the user select component
        user_select = ui.UserSelect(
            placeholder="Select a user",
            min_values=1,
            max_values=1,
            custom_id=f"{action}_user_select"
        )
        user_select.callback = self.user_select_callback
        self.add_item(user_select)

    async def user_select_callback(self, interaction: Interaction):
        selected_user = interaction.data['values'][0]
        selected_user = interaction.guild.get_member(int(selected_user))
        channel = interaction.guild.get_channel(self.channel_id)

        if not channel:
            await interaction.response.send_message("❌ Channel not found.", ephemeral=True)
            return

        # Verify the user still owns this channel
        temp_channel_data = self.db.get_temp_channel(self.channel_id)
        if not temp_channel_data or temp_channel_data['owner_id'] != interaction.user.id:
            await interaction.response.send_message("❌ You don't own this channel or it no longer exists.", ephemeral=True)
            return

        if self.action == "kick":
            await self.handle_kick_user(interaction, selected_user, channel)
        elif self.action == "block":
            await self.handle_block_user(interaction, selected_user, channel)
        elif self.action == "unblock":
            await self.handle_unblock_user(interaction, selected_user, channel)
        elif self.action == "transfer":
            await self.handle_transfer_ownership(interaction, selected_user, channel)

    async def handle_kick_user(self, interaction: Interaction, user: discord.Member, channel: discord.VoiceChannel):
        if user == interaction.user:
            await interaction.response.send_message("❌ You cannot kick yourself.", ephemeral=True)
            return

        if user not in channel.members:
            await interaction.response.send_message("❌ That user is not in your voice channel.", ephemeral=True)
            return

        try:
            await user.move_to(None)
            await interaction.response.send_message(f"✅ Kicked {user.display_name} from your voice channel.", ephemeral=True)
        except Exception as e:
            logger.error(f"Error kicking user: {e}")
            await interaction.response.send_message("❌ An error occurred while kicking the user.", ephemeral=True)

    async def handle_block_user(self, interaction: Interaction, user: discord.Member, channel: discord.VoiceChannel):
        if user == interaction.user:
            await interaction.response.send_message("❌ You cannot block yourself.", ephemeral=True)
            return

        try:
            # Block user by denying connect permission
            await channel.set_permissions(user, connect=False)

            # Kick them if they're currently in the channel
            if user in channel.members:
                await user.move_to(None)

            # Update database
            self.db.block_user_from_temp_channel(self.channel_id, user.id)

            await interaction.response.send_message(f"⛔ Blocked {user.display_name} from your voice channel.", ephemeral=True)
        except Exception as e:
            logger.error(f"Error blocking user: {e}")
            await interaction.response.send_message("❌ An error occurred while blocking the user.", ephemeral=True)

    async def handle_unblock_user(self, interaction: Interaction, user: discord.Member, channel: discord.VoiceChannel):
        try:
            # Unblock user by removing the permission override
            await channel.set_permissions(user, connect=None)

            # Update database
            self.db.unblock_user_from_temp_channel(self.channel_id, user.id)

            await interaction.response.send_message(f"✅ Unblocked {user.display_name} from your voice channel.", ephemeral=True)
        except Exception as e:
            logger.error(f"Error unblocking user: {e}")
            await interaction.response.send_message("❌ An error occurred while unblocking the user.", ephemeral=True)

    async def handle_transfer_ownership(self, interaction: Interaction, user: discord.Member, channel: discord.VoiceChannel):
        if user == interaction.user:
            await interaction.response.send_message("❌ You are already the owner.", ephemeral=True)
            return

        if user not in channel.members:
            await interaction.response.send_message("❌ That user must be in your voice channel to transfer ownership.", ephemeral=True)
            return

        # Check if target user already has a temp channel
        existing_channel = self.db.get_user_temp_channel(interaction.guild_id, user.id)
        if existing_channel and existing_channel['channel_id'] != self.channel_id:
            await interaction.response.send_message("❌ That user already owns a temporary voice channel.", ephemeral=True)
            return

        try:
            # Update channel name
            new_name = f"{user.display_name}'s Channel"
            await channel.edit(name=new_name[:100])

            # Update database
            self.db.update_temp_channel_owner(self.channel_id, user.id)

            await interaction.response.send_message(f"🔄 Transferred ownership to {user.display_name}.", ephemeral=True)
        except Exception as e:
            logger.error(f"Error transferring ownership: {e}")
            await interaction.response.send_message("❌ An error occurred while transferring ownership.", ephemeral=True)


# TempVoice Modals
class UserLimitModal(ui.Modal, title="Set User Limit"):
    def __init__(self, db: DatabaseManager, channel_id: int):
        super().__init__()
        self.db = db
        self.channel_id = channel_id

    user_limit = ui.TextInput(
        label="User Limit",
        placeholder="Enter user limit (0 for no limit, max 99)",
        required=True,
        max_length=2
    )

    async def on_submit(self, interaction: Interaction):
        try:
            limit = int(self.user_limit.value)
            if limit < 0 or limit > 99:
                await interaction.response.send_message("❌ User limit must be between 0 and 99.", ephemeral=True)
                return
        except ValueError:
            await interaction.response.send_message("❌ Please enter a valid number.", ephemeral=True)
            return

        channel = interaction.guild.get_channel(self.channel_id)
        if not channel:
            await interaction.response.send_message("❌ Channel not found.", ephemeral=True)
            return

        try:
            # Set the user limit (0 means no limit)
            await channel.edit(user_limit=limit if limit > 0 else None)

            # Update database
            self.db.set_temp_channel_limit(self.channel_id, limit if limit > 0 else None)

            limit_text = f"{limit} users" if limit > 0 else "no limit"
            await interaction.response.send_message(f"✅ User limit set to {limit_text}.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error setting user limit: {e}")
            await interaction.response.send_message("❌ An error occurred while setting the user limit.", ephemeral=True)


# Sticky Message Modal
class StickyMessageModal(ui.Modal, title="Create Sticky Message"):
    def __init__(self, db: DatabaseManager, channel_id: int):
        super().__init__()
        self.db = db
        self.channel_id = channel_id

    content = ui.TextInput(
        label="Sticky Message Content",
        placeholder="Enter your sticky message content...",
        style=discord.TextStyle.long,
        required=True,
        max_length=4000
    )

    async def on_submit(self, interaction: Interaction):
        try:
            content = self.content.value.strip()
            if not content:
                await interaction.response.send_message("❌ Sticky message content cannot be empty.", ephemeral=True)
                return

            # Save sticky message to database
            success = self.db.set_sticky_message(
                interaction.guild.id,
                self.channel_id,
                content,
                interaction.user.id
            )

            if not success:
                await interaction.response.send_message("❌ Failed to save sticky message.", ephemeral=True)
                return

            # Get the channel
            channel = interaction.guild.get_channel(self.channel_id)
            if not channel:
                await interaction.response.send_message("❌ Channel not found.", ephemeral=True)
                return

            # Create and send the sticky message embed
            embed = discord.Embed(
                title="📌 Stickied Message",
                description=content,
                color=discord.Color.orange(),
                timestamp=datetime.now(timezone.utc)
            )
            embed.set_footer(text=f".gg/leakin")

            try:
                sticky_msg = await channel.send(embed=embed)

                # Update database with message ID
                self.db.update_sticky_message_id(interaction.guild.id, self.channel_id, sticky_msg.id)

                # Log the activity
                self.db.log_sticky_activity(
                    interaction.guild.id,
                    self.channel_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    "created",
                    content
                )

                # Send log to log channel if configured
                config = self.db.get_server_config(interaction.guild.id)
                if config and config.get('log_channel_id'):
                    log_channel = interaction.guild.get_channel(config['log_channel_id'])
                    if log_channel:
                        log_embed = discord.Embed(
                            title="📌 Sticky Message Created",
                            color=discord.Color.green(),
                            timestamp=datetime.now(timezone.utc)
                        )
                        log_embed.add_field(name="User", value=f"{interaction.user.mention} ({interaction.user.id})", inline=True)
                        log_embed.add_field(name="Channel", value=channel.mention, inline=True)
                        log_embed.add_field(name="Content", value=content[:1000] + ("..." if len(content) > 1000 else ""), inline=False)

                        try:
                            await log_channel.send(embed=log_embed)
                        except Exception as e:
                            logger.error(f"Failed to send sticky log: {e}")

                await interaction.response.send_message(f"✅ Sticky message created in {channel.mention}!", ephemeral=True)

            except discord.Forbidden:
                await interaction.response.send_message("❌ I don't have permission to send messages in that channel.", ephemeral=True)
            except Exception as e:
                logger.error(f"Error sending sticky message: {e}")
                await interaction.response.send_message("❌ An error occurred while creating the sticky message.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in sticky message modal: {e}")
            await interaction.response.send_message("❌ An error occurred while processing your request.", ephemeral=True)


@bot.tree.command(name="temp-voice", description="Set up TempVoice system")
@app_commands.describe(
    interface_channel="Channel where the TempVoice interface embed will be sent",
    creator_channel="Voice channel whose permissions will be copied for each temporary voice channel",
    default_user_limit="Default user limit for new temp channels (0 for no limit, max 99)"
)
@require_license(bot.db)
async def temp_voice(
    interaction: discord.Interaction,
    interface_channel: discord.TextChannel,
    creator_channel: discord.VoiceChannel,
    default_user_limit: int = 0
):
    """Set up the TempVoice system"""
    # Verify permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command.", ephemeral=True)
        return

    # Validate default user limit
    if default_user_limit < 0 or default_user_limit > 99:
        await interaction.response.send_message("❌ Default user limit must be between 0 and 99 (0 = no limit).", ephemeral=True)
        return

    # Check if bot has necessary permissions
    bot_member = interaction.guild.me
    if not bot_member.guild_permissions.manage_channels:
        await interaction.response.send_message("❌ I need 'Manage Channels' permission to create temporary voice channels.", ephemeral=True)
        return

    if not bot_member.guild_permissions.move_members:
        await interaction.response.send_message("❌ I need 'Move Members' permission to move users to their channels.", ephemeral=True)
        return

    # Check if bot can send messages in the interface channel
    if not interface_channel.permissions_for(bot_member).send_messages:
        await interaction.response.send_message("❌ I don't have permission to send messages in the interface channel.", ephemeral=True)
        return

    # Check if bot can view the creator channel
    if not creator_channel.permissions_for(bot_member).view_channel:
        await interaction.response.send_message("❌ I don't have permission to view the creator voice channel.", ephemeral=True)
        return

    # Save settings
    success = bot.db.set_tempvoice_settings(
        interaction.guild_id,
        interface_channel.id,
        creator_channel.id,
        default_user_limit if default_user_limit > 0 else None
    )

    if not success:
        await interaction.response.send_message("❌ Failed to save TempVoice settings.", ephemeral=True)
        return

    # Create and send the TempVoice interface embed
    default_limit_text = f" (Default limit: {default_user_limit})" if default_user_limit > 0 else " (No default limit)"

    embed = Embed(
        title="TempVoice Interface",
        description=(
            f"**To create a temporary voice channel:** Join {creator_channel.mention}\n"
            f"Your channel will be created automatically and you'll be moved to it!{default_limit_text}\n\n"
            "**Button Functions:**\n"
            "➕ - Set User Limit\n"
            "👢 - Kick User\n"
            "⛔ - Block User\n"
            "✅ - Unblock User\n"
            "🔒 - Lock Channel\n"
            "🔓 - Unlock Channel\n"
            "🔄 - Transfer Ownership"
        ),
        color=Color.blue()
    )
    embed.set_footer(text="Press the buttons below to use the interface.")

    view = TempVoiceView(bot.db)
    await interface_channel.send(embed=embed, view=view)

    await interaction.response.send_message("✅ TempVoice system has been set up successfully!", ephemeral=True)


@bot.tree.command(name="gender-verification", description="Set up gender verification system")
@app_commands.describe(
    channel="Channel where the verification message will be sent",
    category="Category where verification tickets will be created",
    supportrole="Role that can manage verification tickets",
    papertext="Text that users must write on paper for verification"
)
@require_license(bot.db)
async def gender_verification(
    interaction: discord.Interaction,
    channel: discord.TextChannel,
    category: discord.CategoryChannel,
    supportrole: discord.Role,
    papertext: str
):
    """Set up the gender verification system"""
    # Verify permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command.", ephemeral=True)
        return
        
    # Save settings
    success = bot.db.set_gender_verification_settings(
        interaction.guild_id,
        channel.id,
        category.id,
        supportrole.id,
        papertext
    )
    
    if not success:
        await interaction.response.send_message("❌ Failed to save verification settings.", ephemeral=True)
        return
    
    # Create and send the verification embed
    embed = Embed(
        title="Verify your gender",
        description="Please click the button below to verify your gender (female, male).",
        color=Color.blue()
    )
    embed.set_footer(text=".gg/leakin")
    
    view = GenderVerificationView(bot.db)
    await channel.send(embed=embed, view=view)
    
    await interaction.response.send_message("✅ Gender verification system has been set up successfully!", ephemeral=True)


@bot.tree.command(name="vent-setup", description="Set up anonymous venting system")
@app_commands.describe(
    channel="Channel where anonymous vent messages will be sent"
)
@require_license(bot.db)
async def vent_setup(interaction: discord.Interaction, channel: discord.TextChannel):
    """Set up the anonymous venting system"""
    # Check if user is server owner
    if not is_server_owner(interaction.user, interaction.guild):
        await interaction.response.send_message("❌ Only the server owner can configure the vent system.", ephemeral=True)
        return

    # Check if log channel is configured (required for vent logging)
    config = bot.db.get_server_config(interaction.guild.id)
    if not config or not config.get('log_channel_id'):
        embed = format_license_embed(
            "❌ Log Channel Required",
            "You must set up a log channel first before configuring the vent system.\n\n"
            "Use `/set-log-id <channel>` to configure a log channel for moderation purposes.",
            discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Verify log channel still exists
    log_channel = interaction.guild.get_channel(config['log_channel_id'])
    if not log_channel:
        embed = format_license_embed(
            "❌ Log Channel Not Found",
            "The configured log channel no longer exists.\n\n"
            "Use `/set-log-id <channel>` to reconfigure a log channel.",
            discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Save vent settings
    success = bot.db.set_vent_settings(interaction.guild.id, channel.id)

    if success:
        embed = format_license_embed(
            "✅ Vent System Configuration Updated",
            f"Vent channel set to: {channel.mention}\n"
            f"Log channel: {log_channel.mention}\n\n"
            "Users can now use `/vent <message>` to send anonymous messages to the vent channel.\n\n"
            "**Note:** All vent messages are logged to the log channel for moderation purposes.",
            discord.Color.green()
        )
    else:
        embed = format_license_embed(
            "❌ Configuration Failed",
            "Failed to update vent system configuration.",
            discord.Color.red()
        )

    await interaction.response.send_message(embed=embed, ephemeral=True)


@bot.tree.command(name="vent", description="Send an anonymous message to the vent channel")
@app_commands.describe(
    message="Your anonymous message (keep it appropriate)"
)
@require_license(bot.db)
async def vent(interaction: discord.Interaction, message: str):
    """Send an anonymous vent message"""
    # Check message length
    if len(message.strip()) == 0:
        await interaction.response.send_message("❌ Your vent message cannot be empty.", ephemeral=True)
        return

    if len(message) > 2000:
        await interaction.response.send_message("❌ Your vent message is too long. Please keep it under 2000 characters.", ephemeral=True)
        return

    # Check if log channel is configured (required for vent logging)
    config = bot.db.get_server_config(interaction.guild.id)
    if not config or not config.get('log_channel_id'):
        await interaction.response.send_message("❌ The vent system requires a log channel to be configured. Ask an administrator to use `/set-log-id <channel>` first.", ephemeral=True)
        return

    # Get vent settings
    vent_settings = bot.db.get_vent_settings(interaction.guild.id)
    if not vent_settings:
        await interaction.response.send_message("❌ The vent system has not been set up on this server. Ask an administrator to use `/vent-setup`.", ephemeral=True)
        return

    # Get vent channel
    vent_channel = interaction.guild.get_channel(vent_settings['vent_channel_id'])
    if not vent_channel:
        await interaction.response.send_message("❌ The configured vent channel no longer exists. Please ask an administrator to reconfigure the system.", ephemeral=True)
        return

    # Send anonymous message to vent channel
    try:
        vent_embed = discord.Embed(
            title="💭 Anonymous Vent",
            description=message,
            color=discord.Color.purple(),
            timestamp=datetime.now(timezone.utc)
        )
        vent_embed.set_footer(text="This message was sent anonymously | use /vent <message>")

        await vent_channel.send(embed=vent_embed)

        # Log the vent message for moderation
        bot.db.log_vent_message(
            interaction.guild.id,
            interaction.user.id,
            f"{interaction.user.name}#{interaction.user.discriminator}",
            message
        )

        # Send log to log channel if configured
        config = bot.db.get_server_config(interaction.guild.id)
        if config and config.get('log_channel_id'):
            log_channel = interaction.guild.get_channel(config['log_channel_id'])
            if log_channel:
                log_embed = discord.Embed(
                    title="🔍 Vent Message Logged",
                    color=discord.Color.orange(),
                    timestamp=datetime.now(timezone.utc)
                )
                log_embed.add_field(name="User", value=f"{interaction.user.mention} ({interaction.user.id})", inline=True)
                log_embed.add_field(name="Channel", value=vent_channel.mention, inline=True)
                log_embed.add_field(name="Message", value=message[:1000] + ("..." if len(message) > 1000 else ""), inline=False)

                try:
                    await log_channel.send(embed=log_embed)
                except Exception as e:
                    logger.error(f"Failed to send vent log: {e}")

        await interaction.response.send_message("✅ Your anonymous message has been sent to the vent channel.", ephemeral=True)

    except Exception as e:
        logger.error(f"Error sending vent message: {e}")
        await interaction.response.send_message("❌ Failed to send your vent message. Please try again later.", ephemeral=True)


@bot.tree.command(name="stick", description="Create a persistent sticky message in a channel")
@app_commands.describe(
    channel="Channel where the sticky message will be posted"
)
@require_license(bot.db)
async def stick(interaction: discord.Interaction, channel: discord.TextChannel):
    """Create a sticky message in the specified channel"""
    # Check if user has administrator permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command.", ephemeral=True)
        return

    # Check if logs channel is configured
    config = bot.db.get_server_config(interaction.guild.id)
    if not config or not config.get('log_channel_id'):
        embed = format_license_embed(
            "❌ Configuration Required",
            "Please set up a logs channel first using `/set-channel-id` before using sticky messages.",
            discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Check if bot has permissions in the target channel
    bot_member = interaction.guild.get_member(bot.user.id)
    channel_perms = channel.permissions_for(bot_member)

    if not channel_perms.send_messages or not channel_perms.embed_links:
        await interaction.response.send_message(
            f"❌ I don't have permission to send messages or embed links in {channel.mention}.",
            ephemeral=True
        )
        return

    # Check if there's already a sticky message in this channel
    existing_sticky = bot.db.get_sticky_message(interaction.guild.id, channel.id)
    if existing_sticky:
        await interaction.response.send_message(
            f"❌ There's already a sticky message in {channel.mention}. Use `/unstick` to remove it first.",
            ephemeral=True
        )
        return

    # Show the modal for sticky message content
    modal = StickyMessageModal(bot.db, channel.id)
    await interaction.response.send_modal(modal)


@bot.tree.command(name="unstick", description="Remove the sticky message from a channel")
@app_commands.describe(
    channel="Channel to remove the sticky message from"
)
@require_license(bot.db)
async def unstick(interaction: discord.Interaction, channel: discord.TextChannel):
    """Remove a sticky message from the specified channel"""
    # Check if user has administrator permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command.", ephemeral=True)
        return

    # Check if there's a sticky message in this channel
    sticky_data = bot.db.get_sticky_message(interaction.guild.id, channel.id)
    if not sticky_data:
        await interaction.response.send_message(
            f"❌ There's no sticky message in {channel.mention}.",
            ephemeral=True
        )
        return

    await interaction.response.defer(ephemeral=True)

    try:
        # Try to delete the existing sticky message if it exists
        if sticky_data.get('message_id'):
            try:
                message = await channel.fetch_message(sticky_data['message_id'])
                await message.delete()
            except discord.NotFound:
                # Message was already deleted, that's fine
                pass
            except Exception as e:
                logger.error(f"Error deleting sticky message: {e}")

        # Remove from database
        success = bot.db.remove_sticky_message(interaction.guild.id, channel.id)

        if success:
            # Log the activity
            bot.db.log_sticky_activity(
                interaction.guild.id,
                channel.id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                "removed"
            )

            # Send log to log channel if configured
            config = bot.db.get_server_config(interaction.guild.id)
            if config and config.get('log_channel_id'):
                log_channel = interaction.guild.get_channel(config['log_channel_id'])
                if log_channel:
                    log_embed = discord.Embed(
                        title="📌 Sticky Message Removed",
                        color=discord.Color.orange(),
                        timestamp=datetime.now(timezone.utc)
                    )
                    log_embed.add_field(name="User", value=f"{interaction.user.mention} ({interaction.user.id})", inline=True)
                    log_embed.add_field(name="Channel", value=channel.mention, inline=True)

                    try:
                        await log_channel.send(embed=log_embed)
                    except Exception as e:
                        logger.error(f"Failed to send unstick log: {e}")

            await interaction.followup.send(f"✅ Sticky message removed from {channel.mention}.", ephemeral=True)
        else:
            await interaction.followup.send("❌ Failed to remove sticky message from database.", ephemeral=True)

    except Exception as e:
        logger.error(f"Error in unstick command: {e}")
        await interaction.followup.send("❌ An error occurred while removing the sticky message.", ephemeral=True)


@bot.event
async def on_guild_channel_delete(channel):
    """Handle channel deletion - tickets and temp voice channels"""
    # Handle ticket channel deletion
    if isinstance(channel, discord.TextChannel):
        ticket = bot.db.db['leakin-gender-tickets'].find_one({"channel_id": channel.id, "status": "open"})
        if ticket:
            bot.db.close_gender_verification_ticket(str(ticket['_id']), channel.id)

    # Handle temporary voice channel deletion
    elif isinstance(channel, discord.VoiceChannel):
        temp_channel_data = bot.db.get_temp_channel(channel.id)
        if temp_channel_data:
            bot.db.delete_temp_channel(channel.id)
            logger.info(f"Cleaned up database entry for deleted temp voice channel: {channel.name}")


@bot.event
async def on_member_remove(member):
    """Handle member leaving the server"""
    # Close any open tickets from this user
    ticket = bot.db.db['leakin-gender-tickets'].find_one({
        "user_id": member.id,
        "server_id": member.guild.id,
        "status": "open"
    })
    
    if ticket and ticket.get('channel_id'):
        channel = member.guild.get_channel(ticket['channel_id'])
        if channel:
            try:
                await channel.delete(reason=f"User left the server")
            except Exception as e:
                logger.error(f"Error deleting ticket channel: {e}")
        
        bot.db.close_gender_verification_ticket(str(ticket['_id']), ticket.get('channel_id'))


# Rate limiting for sticky messages (per channel)
sticky_message_cooldowns = {}

@bot.event
async def on_message(message):
    """Handle new messages and repost sticky messages if needed"""
    try:
        # Only process messages in guilds
        if not message.guild:
            return

        # Skip if server doesn't have a license
        if not bot.db.is_server_licensed(message.guild.id):
            return

        # Check if this channel has a sticky message
        sticky_data = bot.db.get_sticky_message(message.guild.id, message.channel.id)
        if not sticky_data:
            return

        # Don't repost if this message IS the sticky message (prevent infinite loop)
        if sticky_data.get('message_id') == message.id:
            return

        # Rate limiting: only repost sticky message once every 5 seconds per channel
        channel_id = message.channel.id
        current_time = datetime.now(timezone.utc)

        if channel_id in sticky_message_cooldowns:
            time_diff = (current_time - sticky_message_cooldowns[channel_id]).total_seconds()
            if time_diff < 5:  # 5 second cooldown
                return

        # Update cooldown
        sticky_message_cooldowns[channel_id] = current_time

        # Try to delete the previous sticky message
        if sticky_data.get('message_id'):
            try:
                old_message = await message.channel.fetch_message(sticky_data['message_id'])
                await old_message.delete()
            except discord.NotFound:
                # Message was already deleted, that's fine
                pass
            except discord.Forbidden:
                logger.warning(f"No permission to delete sticky message in {message.channel.name}")
                return
            except Exception as e:
                logger.error(f"Error deleting old sticky message: {e}")

        # Create and send new sticky message
        try:
            embed = discord.Embed(
                title="📌 Stickied Message",
                description=sticky_data['content'],
                color=discord.Color.orange(),
                timestamp=current_time
            )

            embed.set_footer(text=".gg/leakin")

            new_sticky = await message.channel.send(embed=embed)

            # Update database with new message ID
            bot.db.update_sticky_message_id(message.guild.id, message.channel.id, new_sticky.id)

            # Log the repost activity (but don't spam logs)
            bot.db.log_sticky_activity(
                message.guild.id,
                message.channel.id,
                message.author.id,
                f"{message.author.name}#{message.author.discriminator}",
                "reposted"
            )

        except discord.Forbidden:
            logger.warning(f"No permission to send sticky message in {message.channel.name}")
        except Exception as e:
            logger.error(f"Error sending new sticky message: {e}")

    except Exception as e:
        logger.error(f"Error in on_message sticky handler: {e}", exc_info=True)


# Add cleanup task for old tickets
@tasks.loop(hours=1)
async def cleanup_old_tickets():
    """Clean up tickets older than 24 hours"""
    try:
        # Check if database manager is initialized
        if not hasattr(bot, 'db') or not hasattr(bot.db, 'db'):
            logger.error("Database manager not properly initialized")
            return
            
        # Check database connection
        try:
            # This will raise an exception if not connected
            bot.db.client.admin.command('ping')
        except Exception as e:
            logger.warning("Database connection lost, attempting to reconnect...")
            try:
                bot.db.connect()
                logger.info("Successfully reconnected to database")
            except Exception as connect_error:
                logger.error(f"Failed to reconnect to database: {connect_error}")
                return
                
        # Run cleanup
        try:
            count = bot.db.cleanup_old_tickets()
            if isinstance(count, int) and count > 0:
                logger.info(f"Successfully cleaned up {count} old gender verification tickets")
            elif count == 0:
                logger.debug("No old tickets to clean up")
            else:
                logger.warning(f"Unexpected return value from cleanup_old_tickets: {count}")
                
        except Exception as e:
            logger.error(f"Error during ticket cleanup: {e}", exc_info=True)
            
    except Exception as outer_e:
        logger.critical(f"Critical error in cleanup_old_tickets task: {outer_e}", exc_info=True)


@bot.event
async def on_voice_state_update(member, before, after):
    """Handle voice state updates for TempVoice management"""
    try:
        # Skip if server doesn't have a license
        if not bot.db.is_server_licensed(member.guild.id):
            return

        # Handle user leaving a voice channel
        if before.channel and not after.channel:
            await handle_user_left_voice(member, before.channel)

        # Handle user joining a voice channel
        elif not before.channel and after.channel:
            await handle_user_joined_voice(member, after.channel)

        # Handle user moving between channels
        elif before.channel and after.channel and before.channel != after.channel:
            await handle_user_left_voice(member, before.channel)
            await handle_user_joined_voice(member, after.channel)

    except Exception as e:
        logger.error(f"Error in voice state update handler: {e}", exc_info=True)


async def handle_user_left_voice(member, channel):
    """Handle when a user leaves a voice channel"""
    try:
        # Check if this is a temporary voice channel
        temp_channel_data = bot.db.get_temp_channel(channel.id)
        if not temp_channel_data:
            return

        # If channel is now empty, delete it
        if len(channel.members) == 0:
            try:
                await channel.delete(reason="Temporary voice channel is empty")
                bot.db.delete_temp_channel(channel.id)
            except Exception as e:
                logger.error(f"Error deleting empty temp channel: {e}")

        # If the owner left but others remain, they can claim ownership
        elif temp_channel_data['owner_id'] == member.id and len(channel.members) > 0:
            # The channel remains open for others to claim ownership
            logger.info(f"Owner {member.display_name} left temp channel {channel.name}, others can claim ownership")

    except Exception as e:
        logger.error(f"Error handling user left voice: {e}")


async def handle_user_joined_voice(member, channel):
    """Handle when a user joins a voice channel"""
    try:
        # Check if this is the Creator channel - auto-create temp channel
        settings = bot.db.get_tempvoice_settings(member.guild.id)
        if settings and channel.id == settings['creator_channel_id']:
            await auto_create_temp_channel(member, channel)
            return

        # Check if this is a temporary voice channel
        temp_channel_data = bot.db.get_temp_channel(channel.id)
        if not temp_channel_data:
            return

        # Check if user is blocked
        if member.id in temp_channel_data.get('blocked_users', []):
            try:
                await member.move_to(None)
                logger.info(f"Kicked blocked user {member.display_name} from temp channel {channel.name}")
            except Exception as e:
                logger.error(f"Error kicking blocked user: {e}")
            return

        # If the original owner rejoins their channel, log it (no special permissions needed)
        if temp_channel_data['owner_id'] == member.id:
            logger.info(f"Owner {member.display_name} rejoined their channel {channel.name}")

    except Exception as e:
        logger.error(f"Error handling user joined voice: {e}")


async def auto_create_temp_channel(member, creator_channel):
    """Auto-create a temporary voice channel when user joins the Creator channel"""
    try:
        # Check if user already has a temp channel
        existing_channel = bot.db.get_user_temp_channel(member.guild.id, member.id)
        if existing_channel:
            channel = member.guild.get_channel(existing_channel['channel_id'])
            if channel:
                # Move user to their existing channel
                await member.move_to(channel)
                return
            else:
                # Channel was deleted but not cleaned up in database
                bot.db.delete_temp_channel(existing_channel['channel_id'])

        # Get TempVoice settings for default user limit
        settings = bot.db.get_tempvoice_settings(member.guild.id)
        default_limit = settings.get('default_user_limit') if settings else None

        # Create the temporary voice channel
        # Copy permissions from creator channel
        overwrites = creator_channel.overwrites.copy()

        # Ensure @everyone permissions are copied from creator channel
        if member.guild.default_role in creator_channel.overwrites:
            overwrites[member.guild.default_role] = creator_channel.overwrites[member.guild.default_role]

        channel_name = f"{member.display_name}'s Channel"
        temp_channel = await member.guild.create_voice_channel(
            name=channel_name[:100],  # Ensure channel name is within Discord's limit
            category=creator_channel.category,
            overwrites=overwrites,
            user_limit=default_limit  # Apply default user limit
        )

        # Save to database
        success = bot.db.create_temp_channel(member.guild.id, member.id, temp_channel.id)
        if not success:
            await temp_channel.delete(reason="Failed to save to database")
            logger.error(f"Failed to save temp channel to database for {member.display_name}")
            return

        # Move user to their new channel
        await member.move_to(temp_channel)
        logger.info(f"Auto-created temp channel for {member.display_name}: {temp_channel.name}")

    except Exception as e:
        logger.error(f"Error auto-creating temp channel: {e}")


async def cleanup_temp_voice_channels():
    """Cleanup temp voice channels on bot startup - remove empty channels and handle deleted channels"""
    try:
        logger.info("Starting temp voice channel cleanup...")

        # Get all active temp channels from database
        active_channels = bot.db.get_all_active_temp_channels()
        cleaned_count = 0

        for channel_data in active_channels:
            try:
                guild = bot.get_guild(channel_data['server_id'])
                if not guild:
                    # Guild not found, mark channel as deleted
                    bot.db.delete_temp_channel(channel_data['channel_id'])
                    cleaned_count += 1
                    continue

                channel = guild.get_channel(channel_data['channel_id'])
                if not channel:
                    # Channel was deleted manually, clean up database
                    bot.db.delete_temp_channel(channel_data['channel_id'])
                    cleaned_count += 1
                    continue

                # Check if channel is empty
                if len(channel.members) == 0:
                    try:
                        await channel.delete(reason="Empty temporary voice channel cleanup on bot restart")
                        bot.db.delete_temp_channel(channel_data['channel_id'])
                        cleaned_count += 1
                        logger.info(f"Deleted empty temp channel: {channel.name}")
                    except Exception as e:
                        logger.error(f"Error deleting empty temp channel {channel.name}: {e}")

            except Exception as e:
                logger.error(f"Error processing temp channel {channel_data.get('channel_id', 'unknown')}: {e}")

        if cleaned_count > 0:
            logger.info(f"Temp voice cleanup completed: {cleaned_count} channels cleaned up")
        else:
            logger.info("Temp voice cleanup completed: no channels needed cleanup")

    except Exception as e:
        logger.error(f"Error during temp voice channel cleanup: {e}", exc_info=True)


@bot.event
async def setup_hook():
    """Called when the bot is first started"""
    # Add persistent views
    bot.add_view(GenderVerificationView(bot.db))
    bot.add_view(CloseTicketView(bot.db))
    bot.add_view(TempVoiceView(bot.db))
    
    # Start cleanup task
    cleanup_old_tickets.start()


if __name__ == "__main__":
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("⚠️  Please configure your bot token in the script!")
        print("You need to set:")
        print("- BOT_TOKEN: Your Discord bot token")
        print("- MONGO_URL: Your MongoDB connection string")
        print("- ADMIN_USER_ID: Discord user ID for admin commands")
    else:
        try:
            logger.info("Starting Leakin Bot - Multi-Server License System")
            bot.run(BOT_TOKEN)
        except discord.LoginFailure:
            logger.error("Invalid bot token provided")
        except KeyboardInterrupt:
            logger.info("Bot shutdown requested")
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
        finally:
            # Ensure database connection is closed
            try:
                db.disconnect()
            except:
                pass